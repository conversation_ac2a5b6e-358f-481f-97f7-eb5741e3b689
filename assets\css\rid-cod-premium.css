/* Premium Form Style for RID COD Plugin - Advanced Design */

/* Premium Form Container - Glass Morphism Design */
.rid-cod-form-premium #rid-cod-checkout {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    padding: 0;
    margin: 0 auto 30px;
    max-width: 700px;
    font-family: 'Cairo', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    direction: rtl;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

/* Animated Background */
.rid-cod-form-premium #rid-cod-checkout::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    z-index: -1;
    animation: gradientFlow 8s ease-in-out infinite;
}

@keyframes gradientFlow {
    0%, 100% { 
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    }
    25% { 
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #667eea 100%);
    }
    50% { 
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 50%, #4facfe 100%);
    }
    75% { 
        background: linear-gradient(135deg, #fa709a 0%, #fee140 50%, #43e97b 100%);
    }
}

/* Header Card */
.rid-cod-form-premium .rid-cod-title {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    backdrop-filter: blur(15px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 25px 30px;
    margin: 0;
    border-radius: 24px 24px 0 0;
}

.rid-cod-form-premium .rid-cod-title h3 {
    font-size: 20px;
    margin: 0;
    text-align: center;
    color: #ffffff;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    line-height: 1.4;
}

/* Form Content */
.rid-cod-form-premium #rid-cod-form {
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Customer Info Card */
.rid-cod-form-premium .rid-cod-customer-info {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 25px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

/* Floating Label Fields */
.rid-cod-form-premium .rid-cod-field-group {
    position: relative;
    margin-bottom: 0;
}

.rid-cod-form-premium .floating-label {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    pointer-events: none;
    background: transparent;
    padding: 0 8px;
    z-index: 2;
}

.rid-cod-form-premium #rid-cod-form input[type="text"],
.rid-cod-form-premium #rid-cod-form input[type="tel"],
.rid-cod-form-premium #rid-cod-form input[type="email"],
.rid-cod-form-premium #rid-cod-form select,
.rid-cod-form-premium #rid-cod-form textarea {
    width: 100%;
    padding: 18px 16px 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    font-size: 15px;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: none;
    height: auto;
    min-height: 56px;
    font-weight: 500;
}

.rid-cod-form-premium #rid-cod-form input:focus,
.rid-cod-form-premium #rid-cod-form select:focus,
.rid-cod-form-premium #rid-cod-form textarea:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.rid-cod-form-premium #rid-cod-form input:focus + .floating-label,
.rid-cod-form-premium #rid-cod-form input:not(:placeholder-shown) + .floating-label,
.rid-cod-form-premium #rid-cod-form select:focus + .floating-label,
.rid-cod-form-premium #rid-cod-form textarea:focus + .floating-label {
    top: -8px;
    font-size: 12px;
    color: #ffffff;
    background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
    border-radius: 8px;
    padding: 2px 8px;
    font-weight: 600;
}

.rid-cod-form-premium #rid-cod-form input::placeholder,
.rid-cod-form-premium #rid-cod-form select::placeholder,
.rid-cod-form-premium #rid-cod-form textarea::placeholder {
    color: transparent;
}

/* Variations Card */
.rid-cod-form-premium .rid-cod-variations {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 0;
    position: relative;
}

.rid-cod-form-premium .variation-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.rid-cod-form-premium .variation-label .attribute-label {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.rid-cod-form-premium .variation-boxes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 12px;
    margin-top: 0;
}

/* Premium Variation Options */
.rid-cod-form-premium .variation-option {
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    position: relative;
    overflow: hidden;
    font-size: 14px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rid-cod-form-premium .variation-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.rid-cod-form-premium .variation-option:hover::before {
    left: 100%;
}

.rid-cod-form-premium .variation-option:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.rid-cod-form-premium .variation-option.selected {
    border-color: #ff6b6b;
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

/* Premium Color Swatches */
.rid-cod-form-premium .variation-option.color-option {
    width: 56px;
    height: 56px;
    padding: 6px;
    border-radius: 50%;
    min-width: 56px;
}

.rid-cod-form-premium .color-swatch {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.rid-cod-form-premium .variation-option.color-option.selected {
    border-color: #ffffff;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.rid-cod-form-premium .variation-option.color-option.selected .color-swatch {
    border-color: #ffffff;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Actions Row */
.rid-cod-form-premium .rid-cod-actions-row {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 0;
    padding: 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

/* Premium Submit Button */
.rid-cod-form-premium #rid-cod-submit-btn {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    color: #ffffff;
    border: none;
    padding: 16px 32px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    flex: 1;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.rid-cod-form-premium #rid-cod-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.rid-cod-form-premium #rid-cod-submit-btn:hover::before {
    left: 100%;
}

.rid-cod-form-premium #rid-cod-submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

/* Premium Quantity Selector */
.rid-cod-form-premium .rid-cod-quantity-selector {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.rid-cod-form-premium .rid-cod-quantity-selector button {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 700;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rid-cod-form-premium .rid-cod-quantity-selector button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.rid-cod-form-premium .rid-cod-quantity-selector input {
    border: none;
    width: 60px;
    height: 48px;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    background: transparent;
    margin: 0;
    padding: 0;
    min-height: auto;
}

.rid-cod-form-premium .rid-cod-quantity-selector input:focus {
    outline: none;
    box-shadow: none;
    transform: none;
}

/* Premium Order Summary */
.rid-cod-form-premium #rid-cod-summary-wrapper {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    margin-top: 25px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.rid-cod-form-premium #rid-cod-summary-header {
    background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
    padding: 16px 20px;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.rid-cod-form-premium #rid-cod-summary-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 10px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.rid-cod-form-premium #rid-cod-summary-content {
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
}

.rid-cod-form-premium #rid-cod-summary-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.rid-cod-form-premium #rid-cod-summary-content td {
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

.rid-cod-form-premium #rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    font-weight: 700;
    font-size: 16px;
    color: #ffffff;
    padding-top: 15px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* WhatsApp Button */
.rid-cod-form-premium .rid-cod-whatsapp-button {
    margin-top: 20px;
    padding: 0 30px 30px;
}

.rid-cod-form-premium #rid-cod-whatsapp-btn {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: #ffffff;
    border: none;
    padding: 16px 32px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
    width: 100%;
    justify-content: center;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.rid-cod-form-premium #rid-cod-whatsapp-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(37, 211, 102, 0.4);
    text-decoration: none;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rid-cod-form-premium #rid-cod-checkout {
        margin: 0 10px 20px;
        border-radius: 20px;
    }
    
    .rid-cod-form-premium .rid-cod-title {
        padding: 20px;
        border-radius: 20px 20px 0 0;
    }
    
    .rid-cod-form-premium #rid-cod-form {
        padding: 20px;
        gap: 20px;
    }
    
    .rid-cod-form-premium .rid-cod-customer-info {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 20px;
    }
    
    .rid-cod-form-premium .rid-cod-actions-row {
        flex-direction: column;
        gap: 15px;
        padding: 20px;
    }
    
    .rid-cod-form-premium .rid-cod-submit {
        width: 100%;
    }
    
    .rid-cod-form-premium .variation-boxes {
        grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
        gap: 10px;
    }
    
    .rid-cod-form-premium .variation-option.color-option {
        width: 48px;
        height: 48px;
        min-width: 48px;
    }
}
