# ميزة أشكال النموذج - RID COD Plugin

## نظرة عامة
تم إضافة ميزة جديدة تتيح للمستخدمين اختيار بين ثلاثة تصاميم مختلفة للنموذج:

### 1. النموذج الكلاسيكي (Classic)
- التصميم الأصلي للإضافة
- بسيط وعملي
- مناسب للمواقع التقليدية

### 2. النموذج العصري (Modern)
- تصميم مطابق تماماً للصورة المرجعية
- إطار بنفسجي مستدير
- حقول بدون أيقونات
- خيارات ألوان وأحجام محسنة
- زر بنفسجي مع أزرار كمية
- ملخص طلب قابل للطي

### 3. النموذج المتقدم (Premium) ⭐ جديد
- تصميم Glass Morphism عصري
- خلفية متحركة بألوان متدرجة
- حقول Floating Labels تفاعلية
- تأثيرات بصرية متقدمة
- شفافية وتمويه خلفي (Backdrop Blur)
- تصميم بطاقات منفصلة
- ألوان متدرجة ديناميكية

## كيفية الاستخدام

### للمطورين:
1. انتقل إلى **لوحة التحكم** > **WooCommerce** > **إعدادات الدفع عند الاستلام**
2. اختر تبويبة **"المظهر"**
3. في قسم **"شكل النموذج"** اختر التصميم المطلوب:
   - **النموذج الكلاسيكي**: للتصميم التقليدي
   - **النموذج العصري**: للتصميم الحديث (مطابق للصورة)
   - **النموذج المتقدم**: للتصميم المتطور (Glass Morphism)
4. احفظ الإعدادات

### للمستخدمين النهائيين:
- سيظهر النموذج بالتصميم المختار تلقائياً
- جميع الوظائف تعمل بنفس الطريقة في كلا التصميمين

## الملفات المضافة/المعدلة

### ملفات CSS:
- `assets/css/rid-cod-modern.css` - ملف CSS للتصميم العصري
- `assets/css/rid-cod-premium.css` - ملف CSS للتصميم المتقدم (جديد)

### ملفات PHP المعدلة:
- `includes/class-rid-cod-customizer.php` - إضافة خيار اختيار شكل النموذج
- `includes/class-rid-cod-form.php` - إضافة الكلاس المناسب للنموذج
- `includes/class-rid-cod-loader.php` - تحميل ملف CSS المناسب

### ملفات الاختبار:
- `test-form-styles.html` - ملف معاينة للتصميمين (للاختبار فقط)

## المميزات الجديدة في التصميم العصري

### التصميم العام:
- خلفية متدرجة جذابة
- حدود مستديرة أكثر
- ظلال محسنة
- تأثيرات حركية ناعمة

### الحقول:
- تصميم أكثر عصرية
- تأثيرات تفاعلية عند التركيز
- أيقونات محسنة
- انتقالات ناعمة

### خيارات المنتج:
- تصميم محسن للألوان والأحجام
- تأثيرات بصرية عند التحديد
- تخطيط أفضل للخيارات

### الأزرار:
- تدرجات لونية جذابة
- تأثيرات حركية
- تصميم أكثر حداثة

### ملخص الطلب:
- تصميم محسن
- خلفية متدرجة
- تنظيم أفضل للمعلومات

## المميزات الجديدة في التصميم المتقدم

### Glass Morphism Design:
- خلفية شفافة مع تأثير التمويه
- حدود شفافة وظلال ناعمة
- تأثير الزجاج العصري

### الخلفية المتحركة:
- تدرجات لونية متغيرة
- انتقالات ناعمة بين الألوان
- حركة مستمرة وجذابة

### Floating Labels:
- تسميات تطفو عند التركيز
- تأثيرات انتقالية ناعمة
- تصميم تفاعلي متطور

### البطاقات المنفصلة:
- كل قسم في بطاقة منفصلة
- خلفية شفافة لكل بطاقة
- تنظيم أفضل للمحتوى

### التأثيرات البصرية:
- ظلال متدرجة
- تأثيرات الحركة عند التمرير
- انتقالات ناعمة للعناصر
- ألوان متدرجة ديناميكية

## التوافق
- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب للهواتف المحمولة
- يعمل مع جميع قوالب WordPress

## الإعدادات الافتراضية
- التصميم الافتراضي: **الكلاسيكي**
- يمكن تغييره من إعدادات المظهر

## ملاحظات للمطورين
- يتم تحميل ملف CSS العصري فقط عند اختياره لتحسين الأداء
- جميع المتغيرات CSS الموجودة تعمل مع التصميم الجديد
- يمكن تخصيص الألوان من إعدادات المظهر

## الدعم
في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق الدعم.
