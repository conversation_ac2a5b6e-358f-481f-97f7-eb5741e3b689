<?php

// Ensure the Google Sheets settings class is loaded only once
require_once RID_COD_PLUGIN_DIR . 'includes/class-rid-cod-google-sheets-settings.php';

/**
 * Customizer settings class
 */
class RID_COD_Customizer {
    private $google_sheets_settings_handler; // Property to hold the Google Sheets settings handler instance

    /**
     * Constructor
     */
    public function __construct() {
        // Instantiate the Google Sheets settings handler (class definition is already loaded)
       $this->google_sheets_settings_handler = new RID_COD_Google_Sheets_Settings();

        add_action('admin_menu', array($this, 'add_settings_page'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // AJAX action for clearing shipping cache (admin only)
        add_action('wp_ajax_rid_cod_clear_shipping_cache', array($this, 'handle_clear_shipping_cache_ajax'));

        // Hook for file upload handling needs to be adjusted or integrated into sanitize callback
        // We will integrate file handling into the main sanitize callback

        // Add hook to clear cache when settings are updated
        add_action('update_option', array($this, 'clear_cache_on_settings_update'), 10, 3);
    }

    /**
     * Add settings page
     */
    public function add_settings_page() {
        add_submenu_page(
            'woocommerce',
            __('إعدادات الدفع عند الاستلام', 'rid-cod'),
            __('إعدادات الدفع عند الاستلام', 'rid-cod'),
            'manage_options',
            'rid-cod-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register the main setting group (though we save individual options for simplicity here)
        // register_setting('rid_cod_settings', 'rid_cod_settings_options', array($this, 'sanitize_main_settings')); // Use a single option array AND add sanitize callback

        // --- Define Settings Sections ---
        add_settings_section(
            'rid_cod_section_country', // Section ID
            __('إعدادات الدولة', 'rid-cod'), // Title
            array($this, 'render_country_section_description'), // Callback for description
            'rid_cod_settings' // Page slug where this section appears
        );

        // Form Control Section
        add_settings_section(
            'rid_cod_section_form_control', // Section ID
            __('إعدادات التحكم في النموذج', 'rid-cod'), // Title
            array($this, 'render_form_control_section_description'), // Callback for description
            'rid_cod_settings' // Page slug where this section appears
        );

        add_settings_section(
            'rid_cod_section_labels', // Section ID
            __('تسميات النموذج والنصوص', 'rid-cod'), // Title
            null, // Optional callback function to display content before fields
            'rid_cod_settings' // Page slug where this section appears
        );

        // Split Functionality into specific sections
        add_settings_section(
            'rid_cod_section_whatsapp',
            __('تكامل الواتساب', 'rid-cod'),
            null,
            'rid_cod_settings'
        );
        add_settings_section(
            'rid_cod_section_delivery',
            __('خيارات التوصيل', 'rid-cod'),
            null,
            'rid_cod_settings'
        );
         add_settings_section(
            'rid_cod_section_antispam',
            __('مكافحة الطلبات الوهمية', 'rid-cod'),
            null,
            'rid_cod_settings'
         );

         add_settings_section(
           'rid_cod_section_google_sheets', // Section ID
           __('تكامل جوجل شيتس', 'rid-cod'), // Title
           array($this->google_sheets_settings_handler, 'render_section_description'), // Use callback from handler class
           'rid_cod_settings' // Page slug
       );

         add_settings_section(
            'rid_cod_section_shipping_costs', // Section ID
            __('تكاليف الشحن متعددة الدول', 'rid-cod'), // Title
            array($this, 'render_shipping_costs_section_description'), // Callback for description
            'rid_cod_settings' // Page slug
        );

         add_settings_section(
            'rid_cod_section_shipping', // Section ID
            __('وضع اسعار التوصيل للولايات', 'rid-cod'), // Title
             array($this, 'render_shipping_section_description'), // Callback for description
            'rid_cod_settings'
        );

         add_settings_section(
            'rid_cod_section_appearance', // Section ID
            __('إعدادات المظهر', 'rid-cod'), // Title
            null, // Optional callback function
            'rid_cod_settings' // Page slug
        );

        // --- Register Individual Settings ---
        // Note: We are registering individual settings here for simplicity,
        // although using a single option array ('rid_cod_settings_options') is often preferred.
        // The sanitize callback 'sanitize_main_settings' will still receive the full $input array.

        // Country Settings
        register_setting('rid_cod_settings', 'rid_cod_selected_country', ['sanitize_callback' => array($this, 'sanitize_country_code'), 'default' => 'DZ']);

        // Labels & Texts
        register_setting('rid_cod_settings', 'rid_cod_form_title', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_field_name', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_field_phone', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_field_state', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_field_city', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_products_title', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_product_label', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_price_label', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_field_address', ['sanitize_callback' => 'sanitize_text_field']); // Assuming address might be added later
        register_setting('rid_cod_settings', 'rid_cod_field_notes', ['sanitize_callback' => 'sanitize_text_field']); // Assuming notes might be added later
        register_setting('rid_cod_settings', 'rid_cod_field_quantity', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_button_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_summary_title', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_shipping_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_total_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_success_message', ['sanitize_callback' => 'sanitize_text_field']); // Add setting for success message

        // JavaScript Text Settings
        register_setting('rid_cod_settings', 'rid_cod_select_city_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_select_state_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_select_variation_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_free_shipping_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_processing_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_error_text', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_shipping_unavailable_text', ['sanitize_callback' => 'sanitize_text_field']);

        // Form Control Settings
        register_setting('rid_cod_settings', 'rid_cod_show_notes', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_show_states', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'yes']);
        register_setting('rid_cod_settings', 'rid_cod_show_cities', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'yes']);

        // General Shipping Settings (when states are disabled)
        register_setting('rid_cod_settings', 'rid_cod_general_shipping_home', ['sanitize_callback' => 'floatval', 'default' => 0]);
        register_setting('rid_cod_settings', 'rid_cod_general_shipping_desk', ['sanitize_callback' => 'floatval', 'default' => 0]);

        register_setting('rid_cod_settings', 'rid_cod_enable_sticky_button', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']); // Sticky Button Enable

        // WhatsApp
        register_setting('rid_cod_settings', 'rid_cod_whatsapp_number', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_enable_whatsapp_button', ['sanitize_callback' => array($this, 'sanitize_yes_no')]);

        // Delivery
        register_setting('rid_cod_settings', 'rid_cod_enable_delivery_type', ['sanitize_callback' => array($this, 'sanitize_yes_no')]);
        register_setting('rid_cod_settings', 'rid_cod_delivery_type_home_label', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_delivery_type_desk_label', ['sanitize_callback' => 'sanitize_text_field']);

        // Anti-Spam
        register_setting('rid_cod_settings', 'rid_cod_enable_phone_validation', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'yes']);
        register_setting('rid_cod_settings', 'rid_cod_prevent_autocomplete', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_prevent_copy_paste', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_prevent_2nd_order_24h', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_save_abandoned_order', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_enable_product_control', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_show_color_names', ['sanitize_callback' => array($this, 'sanitize_yes_no_checkbox'), 'default' => 'no']);
        register_setting('rid_cod_settings', 'rid_cod_variation_size', ['sanitize_callback' => array($this, 'sanitize_variation_size'), 'default' => 'medium']);

        // Country Settings
        register_setting('rid_cod_settings', 'rid_cod_selected_country', ['sanitize_callback' => array($this, 'sanitize_country_code'), 'default' => 'DZ']);

        // Google Sheets (handled within the handler class and main sanitize function)
        register_setting('rid_cod_settings', 'rid_cod_gs_options', array($this->google_sheets_settings_handler, 'sanitize_google_sheets_settings')); // Register Google Sheets options separately

        // Legacy Shipping Costs (Algeria only - for backward compatibility)
        $algeria_states = RID_COD_Country_Manager::get_states_by_country('DZ');
        if (!empty($algeria_states)) {
            foreach (array_keys($algeria_states) as $state_code) {
                register_setting('rid_cod_settings', 'rid_cod_cost_state_desk_' . $state_code, ['sanitize_callback' => 'wc_format_decimal']);
                register_setting('rid_cod_settings', 'rid_cod_cost_state_home_' . $state_code, ['sanitize_callback' => 'wc_format_decimal']);
            }
        }

        // Multi-Country Shipping Costs
        $countries = RID_COD_Country_Manager::get_supported_countries();
        foreach (array_keys($countries) as $country_code) {
            register_setting('rid_cod_settings', "rid_cod_shipping_costs_{$country_code}", ['sanitize_callback' => array($this, 'sanitize_shipping_costs')]);
            register_setting('rid_cod_settings', "rid_cod_default_shipping_{$country_code}", ['sanitize_callback' => array($this, 'sanitize_default_shipping')]);
            // Custom states for each country
            register_setting('rid_cod_settings', "rid_cod_custom_states_{$country_code}", ['sanitize_callback' => array($this, 'sanitize_custom_states')]);
        }

        // Appearance Settings
        register_setting('rid_cod_settings', 'rid_cod_form_style', ['sanitize_callback' => 'sanitize_text_field']);
        register_setting('rid_cod_settings', 'rid_cod_color_primary', ['sanitize_callback' => 'sanitize_hex_color']);
        register_setting('rid_cod_settings', 'rid_cod_color_button_bg', ['sanitize_callback' => 'sanitize_hex_color']);
        register_setting('rid_cod_settings', 'rid_cod_color_button_text', ['sanitize_callback' => 'sanitize_hex_color']);
        register_setting('rid_cod_settings', 'rid_cod_color_accent', ['sanitize_callback' => 'sanitize_hex_color']); // Add Accent Color
        // Sticky Button Colors
        register_setting('rid_cod_settings', 'rid_cod_sticky_button_bg_color', ['sanitize_callback' => 'sanitize_hex_color']);
        register_setting('rid_cod_settings', 'rid_cod_sticky_button_text_color', ['sanitize_callback' => 'sanitize_hex_color']);
        register_setting('rid_cod_settings', 'rid_cod_sticky_button_border_color', ['sanitize_callback' => 'sanitize_hex_color']); // Optional border color
        register_setting('rid_cod_settings', 'rid_cod_reset_colors', ['sanitize_callback' => array($this, 'handle_color_reset')]); // Special handler for reset


        // --- Add Fields to Sections ---

        // "Country Settings" Section
        add_settings_field('rid_cod_selected_country', __('اختر الدولة', 'rid-cod'), array($this, 'render_country_select_field'), 'rid_cod_settings', 'rid_cod_section_country', ['id' => 'rid_cod_selected_country', 'default' => 'DZ']);

        // "Form Labels & Texts" Section
        add_settings_field('rid_cod_form_title', __('عنوان النموذج', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_form_title', 'default' => __('للطلب يرجى ملئ الإستمارة أسفله', 'rid-cod')]);
        add_settings_field('rid_cod_field_name', __('نص حقل الاسم', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_field_name', 'default' => __('الاسم الكامل', 'rid-cod')]);
        add_settings_field('rid_cod_field_phone', __('نص حقل الهاتف', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_field_phone', 'default' => __('رقم الهاتف', 'rid-cod')]);
        add_settings_field('rid_cod_field_state', __('نص حقل الولاية', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_field_state', 'default' => __('الولاية', 'rid-cod')]);
        add_settings_field('rid_cod_field_city', __('نص حقل المدينة', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_field_city', 'default' => __('البلدية', 'rid-cod')]);
        add_settings_field('rid_cod_field_quantity', __('تسمية الكمية (غير معروضة)', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_field_quantity', 'default' => __('الكمية', 'rid-cod'), 'desc' => __('هذه التسمية غير معروضة في التصميم الحالي ولكن قد تُستخدم في مكان آخر.', 'rid-cod')]);
        add_settings_field('rid_cod_button_text', __('نص زر الإرسال', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_button_text', 'default' => __('انقر هنا لتأكيد الطلب', 'rid-cod')]);
        add_settings_field('rid_cod_summary_title', __('عنوان الملخص', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_summary_title', 'default' => __('ملخص الطلب', 'rid-cod')]);
        add_settings_field('rid_cod_shipping_text', __('نص الشحن في الملخص', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_shipping_text', 'default' => __('سعر التوصيل', 'rid-cod')]);
        add_settings_field('rid_cod_total_text', __('نص الإجمالي في الملخص', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_total_text', 'default' => __('السعر الإجمالي', 'rid-cod')]);
        add_settings_field('rid_cod_success_message', __('رسالة نجاح الطلب', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_success_message', 'default' => __('تم الطلب بنجاح', 'rid-cod'), 'desc' => __('الرسالة التي تظهر بعد نجاح الطلب.', 'rid-cod')]); // Add field for success message

        // JavaScript Text Settings
        add_settings_field('rid_cod_select_city_text', __('نص اختيار المدينة', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_select_city_text', 'default' => __('اختر البلدية', 'rid-cod')]);
        add_settings_field('rid_cod_select_state_text', __('نص اختيار الولاية', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_select_state_text', 'default' => __('اختر الولاية', 'rid-cod')]);
        add_settings_field('rid_cod_select_variation_text', __('نص اختيار التنويع', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_select_variation_text', 'default' => __('اختر النوع', 'rid-cod')]);
        add_settings_field('rid_cod_free_shipping_text', __('نص الشحن المجاني', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_free_shipping_text', 'default' => __('توصيل مجاني', 'rid-cod')]);
        add_settings_field('rid_cod_processing_text', __('نص المعالجة', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_processing_text', 'default' => __('جاري المعالجة...', 'rid-cod')]);
        add_settings_field('rid_cod_error_text', __('نص الخطأ', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_error_text', 'default' => __('حدث خطأ. يرجى المحاولة مرة أخرى.', 'rid-cod')]);
        add_settings_field('rid_cod_shipping_unavailable_text', __('نص عدم توفر الشحن', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_shipping_unavailable_text', 'default' => __('الشحن غير متوفر', 'rid-cod')]);

        add_settings_field('rid_cod_enable_sticky_button', __('تفعيل زر الشراء الثابت', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_labels', ['id' => 'rid_cod_enable_sticky_button', 'default' => 'no', 'desc' => __('إظهار زر "اشتري الآن" ثابت في الأسفل عند التمرير لأسفل.', 'rid-cod')]);

        // "WhatsApp Integration" Section
        add_settings_field('rid_cod_enable_whatsapp_button', __('تفعيل زر الواتساب', 'rid-cod'), array($this, 'render_select_field'), 'rid_cod_settings', 'rid_cod_section_whatsapp', ['id' => 'rid_cod_enable_whatsapp_button', 'default' => 'yes', 'options' => ['yes' => __('نعم', 'rid-cod'), 'no' => __('لا', 'rid-cod')], 'desc' => __('إظهار زر "الطلب عبر الواتساب" في النموذج.', 'rid-cod')]);
        add_settings_field('rid_cod_whatsapp_number', __('رقم الواتساب', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_whatsapp', ['id' => 'rid_cod_whatsapp_number', 'default' => '', 'placeholder' => __('أدخل رقم الواتساب مع رمز الدولة (مثال: 213xxxxxxxxx)', 'rid-cod'), 'desc' => __('يُستخدم لزر "الطلب عبر الواتساب". اتركه فارغاً لإخفاء الزر إذا كان مفعلاً.', 'rid-cod')]);

        // "Delivery Options" Section
        add_settings_field('rid_cod_enable_delivery_type', __('تفعيل خيار نوع التوصيل', 'rid-cod'), array($this, 'render_select_field'), 'rid_cod_settings', 'rid_cod_section_delivery', ['id' => 'rid_cod_enable_delivery_type', 'default' => 'no', 'options' => ['yes' => __('نعم', 'rid-cod'), 'no' => __('لا', 'rid-cod')], 'desc' => __('إظهار خيارات "التوصيل للمنزل" / "التوصيل للمكتب" في النموذج.', 'rid-cod')]);
        add_settings_field('rid_cod_delivery_type_home_label', __('تسمية التوصيل للمنزل', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_delivery', ['id' => 'rid_cod_delivery_type_home_label', 'default' => __('توصيل للمنزل', 'rid-cod')]);
        add_settings_field('rid_cod_delivery_type_desk_label', __('تسمية التوصيل للمكتب', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_delivery', ['id' => 'rid_cod_delivery_type_desk_label', 'default' => __('توصيل للمكتب', 'rid-cod')]);

        // "Form Control" Section
        add_settings_field('rid_cod_show_notes', __('إظهار حقل الملاحظات', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_show_notes', 'default' => 'no', 'desc' => __('عند التفعيل، سيظهر حقل الملاحظات في نموذج الطلب للعملاء.', 'rid-cod')]);
        add_settings_field('rid_cod_show_states', __('إظهار حقل الولايات', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_show_states', 'default' => 'yes', 'desc' => __('عند إلغاء التفعيل، سيتم إخفاء حقل الولايات من النموذج وسيتم استخدام أسعار التوصيل العامة.', 'rid-cod')]);
        add_settings_field('rid_cod_show_cities', __('إظهار حقل البلديات', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_show_cities', 'default' => 'yes', 'desc' => __('عند إلغاء التفعيل، سيتم إخفاء حقل البلديات من النموذج.', 'rid-cod')]);
        add_settings_field('rid_cod_show_color_names', __('إظهار أسماء الألوان بدلاً من الدوائر', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_show_color_names', 'default' => 'no', 'desc' => __('عند التفعيل، ستظهر أسماء الألوان كنص بدلاً من الدوائر الملونة في نموذج الطلب.', 'rid-cod')]);
        add_settings_field('rid_cod_variation_size', __('حجم متغيرات المنتج', 'rid-cod'), array($this, 'render_select_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_variation_size', 'default' => 'medium', 'options' => ['small' => __('صغير', 'rid-cod'), 'medium' => __('متوسط', 'rid-cod'), 'large' => __('كبير', 'rid-cod'), 'extra-large' => __('كبير جداً', 'rid-cod')], 'desc' => __('اختر حجم متغيرات المنتج (الألوان، الأحجام، إلخ). سيتم تطبيق الحجم تلقائياً على جميع الأجهزة.', 'rid-cod')]);
        add_settings_field('rid_cod_field_notes', __('نص حقل الملاحظات', 'rid-cod'), array($this, 'render_text_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_field_notes', 'default' => __('ملاحظات إضافية (اختياري)', 'rid-cod'), 'desc' => __('النص الذي سيظهر في حقل الملاحظات.', 'rid-cod')]);

        // General Shipping Settings
        add_settings_field('rid_cod_general_shipping_home', __('سعر التوصيل للمنزل (عام)', 'rid-cod'), array($this, 'render_number_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_general_shipping_home', 'default' => 0, 'desc' => __('سعر التوصيل للمنزل عند إخفاء حقل الولايات. يُستخدم عندما يكون نوع التوصيل مفعل أو كافتراضي.', 'rid-cod')]);
        add_settings_field('rid_cod_general_shipping_desk', __('سعر التوصيل للمكتب (عام)', 'rid-cod'), array($this, 'render_number_field'), 'rid_cod_settings', 'rid_cod_section_form_control', ['id' => 'rid_cod_general_shipping_desk', 'default' => 0, 'desc' => __('سعر التوصيل للمكتب عند إخفاء حقل الولايات. يُستخدم عندما يكون نوع التوصيل غير مفعل أو عند اختيار التوصيل للمكتب.', 'rid-cod')]);

        // "Anti-Spam" Section
        add_settings_field('rid_cod_enable_phone_validation', __('تفعيل التحقق من رقم الهاتف', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_antispam', ['id' => 'rid_cod_enable_phone_validation', 'default' => 'yes', 'desc' => __('إذا تم تفعيله، سيتم التحقق من صحة أرقام الهاتف حسب تنسيق الدولة المختارة.', 'rid-cod')]);
        add_settings_field('rid_cod_prevent_autocomplete', __('منع الإكمال التلقائي لحقول النموذج', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_antispam', ['id' => 'rid_cod_prevent_autocomplete', 'default' => 'no', 'desc' => __('تعطيل الإكمال التلقائي للمتصفح في حقول النموذج.', 'rid-cod')]);
        add_settings_field('rid_cod_prevent_copy_paste', __('منع نسخ ولصق المعلومات في نموذج الطلب', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_antispam', ['id' => 'rid_cod_prevent_copy_paste', 'default' => 'no', 'desc' => __('تعطيل عمليات النسخ واللصق داخل حقول النموذج.', 'rid-cod')]);
        add_settings_field('rid_cod_prevent_2nd_order_24h', __('منع الطلب الثاني خلال 24 ساعة', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_antispam', ['id' => 'rid_cod_prevent_2nd_order_24h', 'default' => 'no', 'desc' => __('تحديد العملاء بطلب واحد كل 24 ساعة بناءً على رقم الهاتف أو عنوان IP.', 'rid-cod')]);
        add_settings_field('rid_cod_save_abandoned_order', __('حفظ الطلبات المتروكة', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_antispam', ['id' => 'rid_cod_save_abandoned_order', 'default' => 'no', 'desc' => __('محاولة حفظ الطلبات المملوءة جزئياً إذا غادر المستخدم الصفحة (يتطلب تطبيق).', 'rid-cod')]);
        add_settings_field('rid_cod_enable_product_control', __('تفعيل التحكم بالنموذج على مستوى المنتج', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_antispam', ['id' => 'rid_cod_enable_product_control', 'default' => 'no', 'desc' => __('عند التفعيل، يمكنك التحكم في عرض نموذج الطلب لكل منتج على حدة. ستظهر مربع "إعدادات نموذج الطلب" الجديد في الشريط الجانبي لصفحة تحرير المنتج. المنتجات تُظهر النموذج افتراضياً ما لم يتم تعطيلها بشكل محدد.', 'rid-cod')]);

        // "Google Sheets Integration" Section (Fields rendered by handler class)
        $gs_options = get_option('rid_cod_gs_options', []); // Get Google Sheet options
        add_settings_field(
            'gs_enable_google_sheets',
            __('تفعيل التكامل', 'rid-cod'),
            array($this->google_sheets_settings_handler, 'render_google_sheets_checkbox_field'),
            'rid_cod_settings',
            'rid_cod_section_google_sheets',
            ['id' => 'enable_google_sheets', 'option_group' => 'rid_cod_gs_options', 'value' => $gs_options['enable_google_sheets'] ?? 'no', 'desc' => __('تفعيل إرسال بيانات الطلبات إلى جوجل شيتس.', 'rid-cod')]
        );
        add_settings_field(
            'gs_apps_script_url',
            __('رابط تطبيق الويب للسكريبت', 'rid-cod'),
            array($this->google_sheets_settings_handler, 'render_google_sheets_text_field'),
            'rid_cod_settings',
            'rid_cod_section_google_sheets',
            ['id' => 'apps_script_url', 'option_group' => 'rid_cod_gs_options', 'value' => $gs_options['apps_script_url'] ?? '', 'desc' => __('أدخل رابط تطبيق الويب الذي تم الحصول عليه بعد نشر Google Apps Script الخاص بك.', 'rid-cod'), 'placeholder' => 'https://script.google.com/macros/s/.../exec']
        );
         add_settings_field(
            'gs_sheet_name',
            __('اسم الورقة المستهدفة', 'rid-cod'),
            array($this->google_sheets_settings_handler, 'render_google_sheets_text_field'),
            'rid_cod_settings',
            'rid_cod_section_google_sheets',
            ['id' => 'sheet_name', 'option_group' => 'rid_cod_gs_options', 'value' => $gs_options['sheet_name'] ?? 'Sheet1', 'desc' => __('أدخل اسم ورقة العمل المحددة (التبويب) داخل جوجل شيت حيث يجب إضافة البيانات (مثل Sheet1).', 'rid-cod'), 'placeholder' => 'Sheet1']
        );

        // "Appearance Settings" Section
        // Form Style Selection
        add_settings_field('rid_cod_form_style', __('شكل النموذج', 'rid-cod'), array($this, 'render_form_style_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_form_style', 'default' => 'classic']);

        add_settings_field('rid_cod_color_primary', __('لون الخلفية الأساسي', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_color_primary', 'default' => '#ffffff']); // Updated default from hani.css
        add_settings_field('rid_cod_color_button_bg', __('لون خلفية الزر', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_color_button_bg', 'default' => '#6a3de8']); // Updated default
        add_settings_field('rid_cod_color_button_text', __('لون نص الزر', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_color_button_text', 'default' => '#ffffff']);
        add_settings_field('rid_cod_color_accent', __('اللون المميز', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_color_accent', 'default' => '#6a3de8']); // Add Accent Color Field
        add_settings_field('rid_cod_reset_colors', __('إعادة تعيين الألوان الافتراضية', 'rid-cod'), array($this, 'render_checkbox_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_reset_colors', 'desc' => __('ضع علامة في هذا المربع واحفظ الإعدادات لاستعادة الألوان الافتراضية.', 'rid-cod')]);
        // Sticky Button Colors
        add_settings_field('rid_cod_sticky_button_bg_color', __('لون خلفية الزر الثابت', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_sticky_button_bg_color', 'default' => '#6a3de8']);
        add_settings_field('rid_cod_sticky_button_text_color', __('لون نص الزر الثابت', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_sticky_button_text_color', 'default' => '#ffffff']);
        add_settings_field('rid_cod_sticky_button_border_color', __('لون حدود الزر الثابت', 'rid-cod'), array($this, 'render_color_picker_field'), 'rid_cod_settings', 'rid_cod_section_appearance', ['id' => 'rid_cod_sticky_button_border_color', 'default' => '#6a3de8', 'desc' => __('اختياري: تعيين لون الحدود للزر الثابت.', 'rid-cod')]);

        // "Shipping Costs" Section
        add_settings_field('rid_cod_shipping_costs_manager', __('إدارة تكاليف الشحن متعددة الدول', 'rid-cod'), array($this, 'render_shipping_costs_manager'), 'rid_cod_settings', 'rid_cod_section_shipping_costs');

    } // End of register_settings method

    // --- Callback Functions to Render Fields ---

    public function render_text_field($args) {
        $option_value = get_option($args['id'], $args['default'] ?? '');
        $placeholder = $args['placeholder'] ?? '';
        $class = $args['class'] ?? 'regular-text';
        $name = esc_attr($args['id']); // Use the ID as the name attribute
        ?>
        <input type="text" id="<?php echo esc_attr($args['id']); ?>" name="<?php echo $name; ?>" value="<?php echo esc_attr($option_value); ?>" class="<?php echo esc_attr($class); ?>" placeholder="<?php echo esc_attr($placeholder); ?>" />
        <?php if (isset($args['desc'])) : ?>
            <p class="description"><?php echo esc_html($args['desc']); ?></p>
        <?php endif; ?>
        <?php
    }

    public function render_number_field($args) {
        $option_value = get_option($args['id'], $args['default'] ?? 0);
        $placeholder = $args['placeholder'] ?? '';
        $class = $args['class'] ?? 'regular-text';
        $name = esc_attr($args['id']); // Use the ID as the name attribute
        $min = $args['min'] ?? 0;
        $step = $args['step'] ?? 0.01;
        ?>
        <input type="number" id="<?php echo esc_attr($args['id']); ?>" name="<?php echo $name; ?>" value="<?php echo esc_attr($option_value); ?>" class="<?php echo esc_attr($class); ?>" placeholder="<?php echo esc_attr($placeholder); ?>" min="<?php echo esc_attr($min); ?>" step="<?php echo esc_attr($step); ?>" />
        <?php if (isset($args['desc'])) : ?>
            <p class="description"><?php echo esc_html($args['desc']); ?></p>
        <?php endif; ?>
        <?php
    }

     public function render_select_field($args) {
        $option_value = get_option($args['id'], $args['default'] ?? '');
        $name = esc_attr($args['id']);
        ?>
        <select id="<?php echo esc_attr($args['id']); ?>" name="<?php echo $name; ?>">
            <?php foreach ($args['options'] as $value => $label) : ?>
                <option value="<?php echo esc_attr($value); ?>" <?php selected($option_value, $value); ?>><?php echo esc_html($label); ?></option>
            <?php endforeach; ?>
        </select>
        <?php if (isset($args['desc'])) : ?>
            <p class="description"><?php echo esc_html($args['desc']); ?></p>
        <?php endif; ?>
        <?php
    }

     public function render_checkbox_field($args) {
        // Checkboxes are handled differently - they only submit a value if checked.
        // We don't need to fetch the current value here for rendering the input itself,
        // but the 'checked()' function will use get_option internally if needed.
        $option_value = get_option($args['id'], $args['default'] ?? ''); // Get value for checked() comparison
        $name = esc_attr($args['id']);
        ?>
        <input type="hidden" name="<?php echo $name; ?>" value="no"> <!-- Hidden field to ensure 'no' is saved if unchecked -->
        <input type="checkbox" id="<?php echo esc_attr($args['id']); ?>" name="<?php echo $name; ?>" value="yes" <?php checked($option_value, 'yes'); ?> />
        <?php if (isset($args['desc'])) : ?>
             <label for="<?php echo esc_attr($args['id']); ?>"><span class="description"><?php echo esc_html($args['desc']); ?></span></label>
        <?php endif; ?>
        <?php
    }

     public function render_color_picker_field($args) {
         $option_value = get_option($args['id'], $args['default'] ?? '');
         $name = esc_attr($args['id']);
         ?>
         <input type="text" id="<?php echo esc_attr($args['id']); ?>" name="<?php echo $name; ?>" value="<?php echo esc_attr($option_value); ?>" class="rid-cod-color-picker" data-default-color="<?php echo esc_attr($args['default'] ?? '#ffffff'); ?>" />
         <?php if (isset($args['desc'])) : ?>
             <p class="description"><?php echo esc_html($args['desc']); ?></p>
         <?php endif; ?>
         <?php
     }

     public function render_form_style_field($args) {
         $option_value = get_option($args['id'], $args['default'] ?? 'classic');
         $name = esc_attr($args['id']);
         ?>
         <div class="rid-cod-form-style-selector">
             <div class="form-style-options">
                 <label class="form-style-option <?php echo $option_value === 'classic' ? 'selected' : ''; ?>">
                     <input type="radio" name="<?php echo $name; ?>" value="classic" <?php checked($option_value, 'classic'); ?> />
                     <div class="form-style-preview">
                         <div class="preview-container classic-preview">
                             <div class="preview-title">النموذج الكلاسيكي</div>
                             <div class="preview-form">
                                 <div class="preview-field"></div>
                                 <div class="preview-field"></div>
                                 <div class="preview-button"></div>
                             </div>
                         </div>
                     </div>
                     <span class="style-name"><?php esc_html_e('النموذج الكلاسيكي', 'rid-cod'); ?></span>
                 </label>

                 <label class="form-style-option <?php echo $option_value === 'modern' ? 'selected' : ''; ?>">
                     <input type="radio" name="<?php echo $name; ?>" value="modern" <?php checked($option_value, 'modern'); ?> />
                     <div class="form-style-preview">
                         <div class="preview-container modern-preview">
                             <div class="preview-title">النموذج العصري</div>
                             <div class="preview-form">
                                 <div class="preview-field-row">
                                     <div class="preview-field"></div>
                                     <div class="preview-field"></div>
                                 </div>
                                 <div class="preview-field-row">
                                     <div class="preview-select"></div>
                                     <div class="preview-select"></div>
                                 </div>
                                 <div class="preview-variations">
                                     <div class="preview-color-options">
                                         <div class="preview-color red"></div>
                                         <div class="preview-color green"></div>
                                         <div class="preview-color black"></div>
                                     </div>
                                     <div class="preview-size-options">
                                         <div class="preview-size">XS</div>
                                         <div class="preview-size">S</div>
                                         <div class="preview-size selected">M</div>
                                         <div class="preview-size">L</div>
                                     </div>
                                 </div>
                                 <div class="preview-button-row">
                                     <div class="preview-button-modern"></div>
                                     <div class="preview-quantity">
                                         <div class="qty-btn">-</div>
                                         <div class="qty-input">1</div>
                                         <div class="qty-btn">+</div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                     <span class="style-name"><?php esc_html_e('النموذج العصري', 'rid-cod'); ?></span>
                 </label>
             </div>
         </div>

         <style>
         .rid-cod-form-style-selector {
             margin: 10px 0;
         }

         .form-style-options {
             display: flex;
             gap: 20px;
             flex-wrap: wrap;
         }

         .form-style-option {
             border: 2px solid #ddd;
             border-radius: 8px;
             padding: 15px;
             cursor: pointer;
             transition: all 0.3s ease;
             background: #fff;
             min-width: 200px;
             text-align: center;
         }

         .form-style-option:hover {
             border-color: #6a3de8;
             box-shadow: 0 2px 8px rgba(106, 61, 232, 0.1);
         }

         .form-style-option.selected {
             border-color: #6a3de8;
             background: #f8f7ff;
             box-shadow: 0 2px 8px rgba(106, 61, 232, 0.2);
         }

         .form-style-option input[type="radio"] {
             display: none;
         }

         .form-style-preview {
             margin-bottom: 10px;
         }

         .preview-container {
             background: #f9f9f9;
             border-radius: 6px;
             padding: 10px;
             min-height: 120px;
             position: relative;
         }

         .preview-title {
             font-size: 10px;
             color: #666;
             text-align: center;
             margin-bottom: 8px;
         }

         .preview-form {
             display: flex;
             flex-direction: column;
             gap: 4px;
         }

         .classic-preview .preview-field {
             height: 8px;
             background: #e0e0e0;
             border-radius: 2px;
             margin-bottom: 2px;
         }

         .classic-preview .preview-button {
             height: 12px;
             background: #6a3de8;
             border-radius: 3px;
             margin-top: 4px;
         }

         .modern-preview .preview-field-row {
             display: flex;
             gap: 4px;
             margin-bottom: 3px;
         }

         .modern-preview .preview-field {
             height: 8px;
             background: #e0e0e0;
             border-radius: 2px;
             flex: 1;
         }

         .modern-preview .preview-select {
             height: 8px;
             background: #e0e0e0;
             border-radius: 2px;
             flex: 1;
         }

         .preview-variations {
             margin: 4px 0;
         }

         .preview-color-options {
             display: flex;
             gap: 2px;
             margin-bottom: 3px;
             justify-content: center;
         }

         .preview-color {
             width: 8px;
             height: 8px;
             border-radius: 50%;
         }

         .preview-color.red { background: #ff0000; }
         .preview-color.green { background: #00ff00; }
         .preview-color.black { background: #000000; }

         .preview-size-options {
             display: flex;
             gap: 2px;
             justify-content: center;
         }

         .preview-size {
             width: 12px;
             height: 8px;
             background: #e0e0e0;
             border-radius: 2px;
             font-size: 6px;
             display: flex;
             align-items: center;
             justify-content: center;
             color: #666;
         }

         .preview-size.selected {
             background: #6a3de8;
             color: white;
         }

         .preview-button-row {
             display: flex;
             gap: 4px;
             align-items: center;
             margin-top: 4px;
         }

         .preview-button-modern {
             height: 10px;
             background: #6a3de8;
             border-radius: 2px;
             flex: 1;
         }

         .preview-quantity {
             display: flex;
             gap: 1px;
             align-items: center;
         }

         .qty-btn, .qty-input {
             width: 8px;
             height: 8px;
             background: #e0e0e0;
             border-radius: 1px;
             font-size: 6px;
             display: flex;
             align-items: center;
             justify-content: center;
             color: #666;
         }

         .qty-input {
             background: #fff;
             border: 1px solid #ddd;
         }

         .style-name {
             font-weight: 600;
             color: #333;
             font-size: 14px;
         }

         .form-style-option.selected .style-name {
             color: #6a3de8;
         }
         </style>

         <script>
         jQuery(document).ready(function($) {
             $('.form-style-option').on('click', function() {
                 $('.form-style-option').removeClass('selected');
                 $(this).addClass('selected');
                 $(this).find('input[type="radio"]').prop('checked', true);
             });
         });
         </script>

         <?php if (isset($args['desc'])) : ?>
             <p class="description"><?php echo esc_html($args['desc']); ?></p>
         <?php endif; ?>
         <?php
     }

     public function render_shipping_section_description() {
         $current_country = get_option('rid_cod_selected_country', 'DZ');
         $country_data = RID_COD_Country_Manager::get_country_data($current_country);
         $country_name = $country_data ? $country_data['name'] : $current_country;
         
         echo '<p>' . sprintf(
             esc_html__('إعداد تكاليف الشحن لـ %s. حدد التكاليف الافتراضية التي ستُستخدم لجميع الولايات، ثم اختيارياً يمكنك تجاوز التكاليف لولايات محددة أدناه. إذا قمت بتغيير الدولة في إعدادات الدولة، ستتحدث تكاليف الشحن المعروضة هنا تلقائياً لتُظهر التكاليف للدولة المختارة.', 'rid-cod'),
             esc_html($country_name)
         ) . '</p>';
         echo '<p><strong>' . esc_html__('ملاحظة:', 'rid-cod') . '</strong> ' . esc_html__('هذه التكاليف تُستخدم كاحتياطي إذا لم يتم العثور على طريقة شحن مطابقة في مناطق الشحن الخاصة بـ WooCommerce للولاية المختارة.', 'rid-cod') . '</p>';
     }

     public function render_shipping_costs_section_description() {
         echo '<p>' . esc_html__('إعداد تكاليف الشحن للدول والولايات المختلفة. سيستخدم النظام تلقائياً التكاليف المناسبة بناءً على الدولة المختارة.', 'rid-cod') . '</p>';
     }


    /**
     * Settings page content
     */
    public function settings_page() {
        // Check if country was changed and show success message
        if (isset($_POST['rid_cod_country_changed']) && $_POST['rid_cod_country_changed'] == '1') {
            $new_country = get_option('rid_cod_selected_country', 'DZ');
            $country_data = RID_COD_Country_Manager::get_country_data($new_country);
            $country_name = $country_data ? $country_data['name'] : $new_country;
            
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>' . sprintf(esc_html__('تم تغيير الدولة بنجاح إلى %s. إعدادات الشحن أدناه الآن خاصة بهذه الدولة.', 'rid-cod'), esc_html($country_name)) . '</p>';
            echo '</div>';
        }
        
        // Get the active tab from the URL, default to 'country'
        $active_tab = isset($_GET['tab']) ? sanitize_key($_GET['tab']) : 'country';
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('إعدادات الدفع عند الاستلام', 'rid-cod'); ?></h1>

            <h2 class="nav-tab-wrapper">
                <a href="?page=rid-cod-settings&tab=country" class="nav-tab <?php echo $active_tab == 'country' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('إعدادات الدولة', 'rid-cod'); ?></a>
                <a href="?page=rid-cod-settings&tab=form_control" class="nav-tab <?php echo $active_tab == 'form_control' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('إعدادات النموذج', 'rid-cod'); ?></a>
                <a href="?page=rid-cod-settings&tab=labels" class="nav-tab <?php echo $active_tab == 'labels' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('تسميات النموذج والنصوص', 'rid-cod'); ?></a>
                <a href="?page=rid-cod-settings&tab=whatsapp" class="nav-tab <?php echo $active_tab == 'whatsapp' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('تكامل الواتساب', 'rid-cod'); ?></a>
                <a href="?page=rid-cod-settings&tab=delivery" class="nav-tab <?php echo $active_tab == 'delivery' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('خيارات التوصيل', 'rid-cod'); ?></a>
                <a href="?page=rid-cod-settings&tab=antispam" class="nav-tab <?php echo $active_tab == 'antispam' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('مكافحة الطلبات الوهمية', 'rid-cod'); ?></a>
                <a href="?page=rid-cod-settings&tab=shipping" class="nav-tab <?php echo $active_tab == 'shipping' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('وضع اسعار التوصيل للولايات', 'rid-cod'); ?></a>
               <a href="?page=rid-cod-settings&tab=google_sheets" class="nav-tab <?php echo $active_tab == 'google_sheets' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('جوجل شيتس', 'rid-cod'); ?></a>
               <a href="?page=rid-cod-settings&tab=appearance" class="nav-tab <?php echo $active_tab == 'appearance' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('المظهر', 'rid-cod'); ?></a>
               <a href="?page=rid-cod-settings&tab=shortcode" class="nav-tab <?php echo $active_tab == 'shortcode' ? 'nav-tab-active' : ''; ?>"><?php esc_html_e('الكود المختصر', 'rid-cod'); ?></a>
            </h2>

            <form method="post" action="options.php">
                <?php settings_fields('rid_cod_settings'); // Output nonce, action, and option_page fields for our settings group ?>

                <div id="tab-country" class="tab-content <?php echo $active_tab == 'country' ? 'active' : ''; ?>">
                    <h3><?php esc_html_e('إعدادات الدولة', 'rid-cod'); ?></h3>
                    <?php $this->render_country_section_description(); ?>
                    <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_country'); ?>
                    </table>
                </div>

                <div id="tab-form_control" class="tab-content <?php echo $active_tab == 'form_control' ? 'active' : ''; ?>">
                    <h3><?php esc_html_e('إعدادات التحكم في النموذج', 'rid-cod'); ?></h3>
                    <?php $this->render_form_control_section_description(); ?>
                    <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_form_control'); ?>
                    </table>
                </div>

                <div id="tab-labels" class="tab-content <?php echo $active_tab == 'labels' ? 'active' : ''; ?>">
                    <h3><?php esc_html_e('إعدادات تسميات النموذج والنصوص', 'rid-cod'); ?></h3>
                    <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_labels'); ?>
                    </table>
                </div>

                 <div id="tab-whatsapp" class="tab-content <?php echo $active_tab == 'whatsapp' ? 'active' : ''; ?>">
                     <h3><?php esc_html_e('إعدادات تكامل الواتساب', 'rid-cod'); ?></h3>
                     <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_whatsapp'); ?>
                    </table>
                </div>

                 <div id="tab-delivery" class="tab-content <?php echo $active_tab == 'delivery' ? 'active' : ''; ?>">
                     <h3><?php esc_html_e('إعدادات خيارات التوصيل', 'rid-cod'); ?></h3>
                     <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_delivery'); ?>
                    </table>
                </div>

                 <div id="tab-antispam" class="tab-content <?php echo $active_tab == 'antispam' ? 'active' : ''; ?>">
                     <h3><?php esc_html_e('إعدادات مكافحة الطلبات الوهمية', 'rid-cod'); ?></h3>
                     <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_antispam'); ?>
                    </table>

                    <?php if (get_option('rid_cod_enable_product_control', 'no') === 'yes') : ?>
                        <?php
                        $hidden_products = RID_COD_Product_Meta::get_products_with_hidden_form();
                        $hidden_count = count($hidden_products);
                        ?>
                        <div class="rid-cod-product-stats" style="background: #f9f9f9; padding: 15px; border: 1px solid #ddd; border-radius: 4px; margin-top: 20px;">
                            <h4><?php esc_html_e('إحصائيات التحكم بالمنتجات', 'rid-cod'); ?></h4>
                            <p>
                                <strong><?php esc_html_e('المنتجات ذات نموذج الطلب المخفي:', 'rid-cod'); ?></strong>
                                <?php echo esc_html($hidden_count); ?>
                            </p>
                            <?php if ($hidden_count > 0) : ?>
                                <p class="description">
                                    <?php esc_html_e('يمكنك إدارة إعدادات المنتجات الفردية من خلال تحرير كل منتج في WooCommerce > المنتجات.', 'rid-cod'); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>



                 <div id="tab-shipping" class="tab-content <?php echo $active_tab == 'shipping' ? 'active' : ''; ?>">
                     <h3><?php esc_html_e('وضع اسعار التوصيل للولايات', 'rid-cod'); ?></h3>
                     <?php $this->render_shipping_section_description(); // Render description ?>


                     
                     <?php 
                     $current_country = get_option('rid_cod_selected_country', 'DZ');
                     $country_data = RID_COD_Country_Manager::get_country_data($current_country);
                     $currency_symbol = $country_data ? $country_data['currency_symbol'] : get_woocommerce_currency_symbol();
                     $default_shipping = RID_COD_Shipping_Manager::get_default_shipping_costs($current_country);
                     ?>
                     
                     <!-- Default shipping costs for the selected country -->
                     <h4><?php printf(esc_html__('التكاليف الافتراضية للشحن لـ %s', 'rid-cod'), esc_html($country_data ? $country_data['name'] : $current_country)); ?></h4>
                     
                     <?php 
                     // Check if any shipping costs are configured for this country
                     $all_shipping_costs = RID_COD_Shipping_Manager::get_all_shipping_costs($current_country);
                     $has_any_costs = !empty($all_shipping_costs) || ($default_shipping['home'] > 0) || ($default_shipping['desk'] > 0);
                     
                     if (!$has_any_costs): ?>
                         <div class="notice notice-warning inline">
                             <p><strong><?php esc_html_e('تحذير:', 'rid-cod'); ?></strong> 
                             <?php printf(esc_html__('لم يتم تكوين أي تكاليف شحن لـ %s. يرجى تعيين التكاليف الافتراضية على الأقل أدناه.', 'rid-cod'), esc_html($country_data ? $country_data['name'] : $current_country)); ?></p>
                         </div>
                     <?php endif; ?>
                     
                     <table class="form-table">
                         <tr>
                             <th scope="row"><?php esc_html_e('تكلفة التوصيل للمكتب الافتراضية', 'rid-cod'); ?></th>
                             <td>
                                 <input type="number" step="any" min="0" 
                                        id="rid_cod_default_desk_cost"
                                        name="rid_cod_default_shipping_<?php echo esc_attr($current_country); ?>[desk]" 
                                        value="<?php echo esc_attr($default_shipping['desk']); ?>" 
                                        class="small-text" 
                                        placeholder="<?php echo esc_attr__('مثال: 500', 'rid-cod'); ?>" />
                                 <?php echo esc_html($currency_symbol); ?>
                                 <button type="button" class="button" onclick="ridCodApplyDefaultDesk()" style="margin-left: 10px;">
                                     <?php esc_html_e('تطبيق على جميع الولايات', 'rid-cod'); ?>
                                 </button>
                                 <p class="description"><?php esc_html_e('ستُستخدم هذه التكلفة للولايات التي لم يتم تحديد تكاليف محددة لها.', 'rid-cod'); ?></p>
                             </td>
                         </tr>
                         <tr>
                             <th scope="row"><?php esc_html_e('تكلفة التوصيل للمنزل الافتراضية', 'rid-cod'); ?></th>
                             <td>
                                 <input type="number" step="any" min="0" 
                                        id="rid_cod_default_home_cost"
                                        name="rid_cod_default_shipping_<?php echo esc_attr($current_country); ?>[home]" 
                                        value="<?php echo esc_attr($default_shipping['home']); ?>" 
                                        class="small-text" 
                                        placeholder="<?php echo esc_attr__('مثال: 600', 'rid-cod'); ?>" />
                                 <?php echo esc_html($currency_symbol); ?>
                                 <button type="button" class="button" onclick="ridCodApplyDefaultHome()" style="margin-left: 10px;">
                                     <?php esc_html_e('تطبيق على جميع الولايات', 'rid-cod'); ?>
                                 </button>
                                 <p class="description"><?php esc_html_e('ستُستخدم هذه التكلفة للولايات التي لم يتم تحديد تكاليف محددة لها.', 'rid-cod'); ?></p>
                             </td>
                         </tr>
                         <tr>
                             <td colspan="2">
                                 <button type="button" class="button button-secondary" onclick="ridCodApplyDefaultBoth()" style="font-weight: bold;">
                                     <?php esc_html_e('تطبيق القيم الافتراضية على جميع الولايات (المكتب والمنزل)', 'rid-cod'); ?>
                                 </button>
                                 <p class="description" style="margin-top: 5px;">
                                     <?php esc_html_e('سيتم ملء جميع حقول الولايات بالقيم الافتراضية أعلاه. يمكنك تعديل قيم ولايات محددة بعد ذلك.', 'rid-cod'); ?>
                                 </p>
                             </td>
                         </tr>
                     </table>
                     
                     <!-- State-specific shipping costs -->
                     <h4><?php esc_html_e('تكاليف الشحن المحددة للولايات (اختياري)', 'rid-cod'); ?></h4>
                     <p class="description">
                         <?php esc_html_e('اترك الحقول فارغة لاستخدام التكاليف الافتراضية أعلاه. أدخل تكاليف محددة فقط للولايات التي تتطلب تسعير مختلف.', 'rid-cod'); ?>
                     </p>
                     <?php // Manually render the state shipping costs table ?>
                     <table class="form-table">
                         <?php
                         $states = RID_COD_Country_Manager::get_states_by_country($current_country); // Get states for selected country
                         if (!empty($states)) {
                             ksort($states, SORT_NUMERIC);
                             
                             // Get country data for currency symbol
                             $country_data = RID_COD_Country_Manager::get_country_data($current_country);
                             $currency_symbol = $country_data ? $country_data['currency_symbol'] : get_woocommerce_currency_symbol();
                             
                             // Get existing shipping costs for this country
                             $shipping_costs = RID_COD_Shipping_Manager::get_all_shipping_costs($current_country);
                             ?>
                             <thead>
                                 <tr>
                                     <th style="width: 20px;">⋮⋮</th>
                                     <th><?php esc_html_e('الولاية (الرمز)', 'rid-cod'); ?></th>
                                     <th><?php esc_html_e('تكلفة التوصيل للمكتب', 'rid-cod'); ?></th>
                                     <th><?php esc_html_e('تكلفة التوصيل للمنزل', 'rid-cod'); ?></th>
                                     <th><?php esc_html_e('إجراءات', 'rid-cod'); ?></th>
                                 </tr>
                             </thead>
                             <tbody>
                             <?php
                             foreach ($states as $state_code => $state_name) {
                                 // Use new multi-country shipping system
                                 $desk_cost = isset($shipping_costs[$state_code]['desk']) ? $shipping_costs[$state_code]['desk'] : '';
                                 $home_cost = isset($shipping_costs[$state_code]['home']) ? $shipping_costs[$state_code]['home'] : '';
                                 ?>
                                 <tr valign="top" class="state-row" data-state-code="<?php echo esc_attr($state_code); ?>" data-state-name="<?php echo esc_attr($state_name); ?>">
                                     <td class="drag-handle" style="cursor: move; text-align: center; color: #666;">⋮⋮</td>
                                     <th scope="row"><?php echo esc_html($state_name); ?> (<?php echo esc_html($state_code); ?>)</th>
                                     <td>
                                         <input type="number" step="any" min="0" 
                                                name="rid_cod_shipping_costs_<?php echo esc_attr($current_country); ?>[<?php echo esc_attr($state_code); ?>][desk]" 
                                                value="<?php echo esc_attr($desk_cost); ?>" 
                                                class="small-text rid-cod-state-desk-cost" 
                                                data-state="<?php echo esc_attr($state_code); ?>"
                                                placeholder="<?php echo esc_attr($default_shipping['desk']); ?>" />
                                         <?php echo esc_html($currency_symbol); ?>
                                         <?php if (empty($desk_cost)): ?>
                                             <span class="description"><?php printf(esc_html__('(افتراضي: %s)', 'rid-cod'), number_format($default_shipping['desk'], 2)); ?></span>
                                         <?php endif; ?>
                                     </td>
                                     <td>
                                         <input type="number" step="any" min="0" 
                                                name="rid_cod_shipping_costs_<?php echo esc_attr($current_country); ?>[<?php echo esc_attr($state_code); ?>][home]" 
                                                value="<?php echo esc_attr($home_cost); ?>" 
                                                class="small-text rid-cod-state-home-cost" 
                                                data-state="<?php echo esc_attr($state_code); ?>"
                                                placeholder="<?php echo esc_attr($default_shipping['home']); ?>" />
                                         <?php echo esc_html($currency_symbol); ?>
                                         <?php if (empty($home_cost)): ?>
                                             <span class="description"><?php printf(esc_html__('(افتراضي: %s)', 'rid-cod'), number_format($default_shipping['home'], 2)); ?></span>
                                         <?php endif; ?>
                                     </td>
                                     <td class="state-actions">
                                         <button type="button" class="button button-small edit-state"
                                                 data-state-code="<?php echo esc_attr($state_code); ?>"
                                                 data-state-name="<?php echo esc_attr($state_name); ?>"
                                                 title="<?php esc_attr_e('تعديل الولاية', 'rid-cod'); ?>">
                                             <?php esc_html_e('تعديل', 'rid-cod'); ?>
                                         </button>
                                         <button type="button" class="button button-small delete-state"
                                                 data-state-code="<?php echo esc_attr($state_code); ?>"
                                                 data-state-name="<?php echo esc_attr($state_name); ?>"
                                                 title="<?php esc_attr_e('حذف الولاية', 'rid-cod'); ?>">
                                             <?php esc_html_e('حذف', 'rid-cod'); ?>
                                         </button>
                                     </td>
                                 </tr>
                                 <?php
                             }
                             ?>
                             </tbody>
                             <?php
                         } else {
                             ?>
                              <thead><tr><th colspan="3"><?php printf(esc_html__('لا يمكن تحميل الولايات لـ %s.', 'rid-cod'), esc_html($country_data ? $country_data['name'] : $current_country)); ?></th></tr></thead>
                             <?php
                         }
                         ?>
                     </table>

                     <!-- States Management Actions -->
                     <div class="states-management-actions" style="margin: 20px 0; padding: 15px; background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px;">
                         <h4 style="margin-top: 0;"><?php esc_html_e('إدارة الولايات', 'rid-cod'); ?></h4>
                         <p>
                             <button type="button" id="add-new-state" class="button button-primary">
                                 <?php esc_html_e('إضافة ولاية جديدة', 'rid-cod'); ?>
                             </button>
                             <button type="button" id="reset-to-default" class="button button-secondary">
                                 <?php esc_html_e('استعادة الولايات الافتراضية', 'rid-cod'); ?>
                             </button>
                             <button type="button" id="save-states-order" class="button button-secondary" style="display: none;">
                                 <?php esc_html_e('حفظ ترتيب الولايات', 'rid-cod'); ?>
                             </button>
                         </p>
                         <p class="description">
                             <?php esc_html_e('يمكنك سحب الولايات لإعادة ترتيبها، أو استخدام الأزرار لإضافة/تعديل/حذف الولايات.', 'rid-cod'); ?>
                         </p>
                     </div>

                     <p>
                         <button type="button" id="rid-cod-clear-shipping-cache-btn" class="button">
                             <?php esc_html_e('تحديث ذاكرة التخزين المؤقت للشحن', 'rid-cod'); ?>
                         </button>
                         <span id="rid-cod-cache-status" style="margin-left: 10px; display: none;"></span>
                         <span class="spinner" style="float: none; vertical-align: middle; margin-left: 5px;"></span>
                     </p>
                     <p class="description"><?php esc_html_e('انقر هنا لمسح ذاكرة التخزين المؤقت لأسعار الشحن (مناطق WooCommerce والتكاليف الافتراضية) إذا قمت بإجراء تغييرات.', 'rid-cod'); ?></p>
                </div>

                 <div id="tab-google-sheets" class="tab-content <?php echo $active_tab == 'google_sheets' ? 'active' : ''; ?>">
                    <?php
                    // Manually render the section description here, as do_settings_fields only renders the fields.
                    if (method_exists($this->google_sheets_settings_handler, 'render_section_description')) {
                         $this->google_sheets_settings_handler->render_section_description();
                    }
                    ?>
                    <table class="form-table">
                        <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_google_sheets'); // This renders the fields added to the section ?>
                    </table>
                 </div>

                 <div id="tab-appearance" class="tab-content <?php echo $active_tab == 'appearance' ? 'active' : ''; ?>">
                     <h3><?php esc_html_e('إعدادات المظهر', 'rid-cod'); ?></h3>
                     <table class="form-table">
                         <?php do_settings_fields('rid_cod_settings', 'rid_cod_section_appearance'); ?>
                     </table>
                 </div>

                <div id="tab-shortcode" class="tab-content <?php echo $active_tab == 'shortcode' ? 'active' : ''; ?>">
                    <h3><?php esc_html_e('الكود المختصر', 'rid-cod'); ?></h3>
                    <p><?php esc_html_e('استخدم الكود المختصر التالي لعرض نموذج الدفع عند الاستلام في أي صفحة أو منشور:', 'rid-cod'); ?></p>
                    <table class="form-table">
                        <tr valign="top">
                            <th scope="row"><?php esc_html_e('مثال على الكود المختصر', 'rid-cod'); ?></th>
                            <td>
                                <input type="text" id="rid-cod-shortcode-text" value="[rid_cod_form product_id=YOUR_PRODUCT_ID]" readonly class="regular-text" style="width: auto; margin-right: 10px;">
                                <button type="button" id="rid-cod-copy-shortcode-button" class="button"><?php esc_html_e('نسخ المثال', 'rid-cod'); ?></button>
                                <p class="description"><?php esc_html_e('انقر على الزر لنسخ مثال الكود المختصر. تذكر استبدال YOUR_PRODUCT_ID برقم المنتج الفعلي.', 'rid-cod'); ?></p>
                            </td>
                        </tr>
                        <tr valign="top">
                            <th scope="row"><?php esc_html_e('الاستخدام في صفحة المنتج', 'rid-cod'); ?></th>
                            <td>
                                <p><?php esc_html_e('لعرض النموذج تلقائياً في صفحة منتج واحد (استبدال زر "إضافة إلى السلة" الافتراضي)، استخدم ببساطة:', 'rid-cod'); ?></p>
                                <code>[rid_cod_form]</code>
                                <p class="description"><?php esc_html_e('الإضافة عادة تضيف النموذج تلقائياً إلى صفحات المنتجات، ولكن يمكنك استخدام هذا الكود المختصر إذا لزم الأمر في القوالب المخصصة.', 'rid-cod'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                 <style>
                    /* Basic styling for tabs - can be moved to admin CSS file */
                    .tab-content { display: none; padding: 15px 0; }
                    .tab-content.active { display: block; }
                    .nav-tab-wrapper { margin-bottom: 15px; }
                 </style>
                 
                 <script type="text/javascript">
                 // Apply default desk cost to all states
                 function ridCodApplyDefaultDesk() {
                     var defaultValue = document.getElementById('rid_cod_default_desk_cost').value;
                     if (defaultValue === '' || defaultValue === '0') {
                         alert('<?php echo esc_js(__('يرجى إدخال قيمة صحيحة للتكلفة الافتراضية', 'rid-cod')); ?>');
                         return;
                     }
                     
                     if (confirm('<?php echo esc_js(__('هل تريد تطبيق قيمة التوصيل للمكتب على جميع الولايات؟ سيتم استبدال القيم الحالية.', 'rid-cod')); ?>')) {
                         var deskInputs = document.querySelectorAll('.rid-cod-state-desk-cost');
                         deskInputs.forEach(function(input) {
                             input.value = defaultValue;
                         });
                         alert('<?php echo esc_js(__('تم تطبيق القيمة على جميع ولايات التوصيل للمكتب', 'rid-cod')); ?>');
                     }
                 }
                 
                 // Apply default home cost to all states
                 function ridCodApplyDefaultHome() {
                     var defaultValue = document.getElementById('rid_cod_default_home_cost').value;
                     if (defaultValue === '' || defaultValue === '0') {
                         alert('<?php echo esc_js(__('يرجى إدخال قيمة صحيحة للتكلفة الافتراضية', 'rid-cod')); ?>');
                         return;
                     }
                     
                     if (confirm('<?php echo esc_js(__('هل تريد تطبيق قيمة التوصيل للمنزل على جميع الولايات؟ سيتم استبدال القيم الحالية.', 'rid-cod')); ?>')) {
                         var homeInputs = document.querySelectorAll('.rid-cod-state-home-cost');
                         homeInputs.forEach(function(input) {
                             input.value = defaultValue;
                         });
                         alert('<?php echo esc_js(__('تم تطبيق القيمة على جميع ولايات التوصيل للمنزل', 'rid-cod')); ?>');
                     }
                 }
                 
                 // Apply both default costs to all states
                 function ridCodApplyDefaultBoth() {
                     var defaultDeskValue = document.getElementById('rid_cod_default_desk_cost').value;
                     var defaultHomeValue = document.getElementById('rid_cod_default_home_cost').value;
                     
                     if (defaultDeskValue === '' && defaultHomeValue === '') {
                         alert('<?php echo esc_js(__('يرجى إدخال قيم صحيحة للتكاليف الافتراضية', 'rid-cod')); ?>');
                         return;
                     }
                     
                     if (confirm('<?php echo esc_js(__('هل تريد تطبيق التكاليف الافتراضية على جميع الولايات؟ سيتم استبدال جميع القيم الحالية.', 'rid-cod')); ?>')) {
                         if (defaultDeskValue !== '') {
                             var deskInputs = document.querySelectorAll('.rid-cod-state-desk-cost');
                             deskInputs.forEach(function(input) {
                                 input.value = defaultDeskValue;
                             });
                         }
                         
                         if (defaultHomeValue !== '') {
                             var homeInputs = document.querySelectorAll('.rid-cod-state-home-cost');
                             homeInputs.forEach(function(input) {
                                 input.value = defaultHomeValue;
                             });
                         }
                         
                         alert('<?php echo esc_js(__('تم تطبيق التكاليف الافتراضية على جميع الولايات', 'rid-cod')); ?>');
                     }
                 }
                 </script>
                <?php submit_button(__('حفظ جميع الإعدادات', 'rid-cod')); // Single save button for all settings ?>
            </form>
        </div>
        <?php
    }

    /**
     * Sanitize 'yes' or 'no' select fields.
     */
    public function sanitize_yes_no($input) {
        return (in_array($input, ['yes', 'no'])) ? $input : 'no'; // Default to 'no' if invalid
    }

    /**
     * Sanitize 'yes' or 'no' checkbox fields.
     * Checkboxes only submit their value ('yes' in this case) when checked.
     * If it's not submitted (unchecked), we want to save 'no'.
     */
    public function sanitize_yes_no_checkbox($input) {
        // If the input from the form is 'yes', return 'yes'. Otherwise (if null or any other value), return 'no'.
        return ($input === 'yes') ? 'yes' : 'no';
    }

    /**
     * Sanitize variation size input.
     */
    public function sanitize_variation_size($input) {
        $allowed_sizes = ['small', 'medium', 'large', 'extra-large'];
        return in_array($input, $allowed_sizes) ? $input : 'medium';
    }

    /**
     * Handle the color reset logic during sanitization.
     * This function is hooked to the 'rid_cod_reset_colors' setting.
     */
    public function handle_color_reset($input) {
        // Check if the reset checkbox was checked
        if ($input === 'yes') {
            // Define default colors
            $default_colors = [
                'rid_cod_color_primary'         => '#ffffff', // Updated default from hani.css
                'rid_cod_color_button_bg'       => '#6a3de8',
                'rid_cod_color_button_text'     => '#ffffff',
                'rid_cod_color_accent'          => '#6a3de8',
                // Sticky Button Defaults
                'rid_cod_sticky_button_bg_color'    => '#6a3de8',
                'rid_cod_sticky_button_text_color'  => '#ffffff',
                'rid_cod_sticky_button_border_color'=> '#6a3de8',
            ];

            // Update the color options to their defaults
            foreach ($default_colors as $key => $default_value) {
                update_option($key, $default_value);
            }

            // Add a success notice
            add_settings_error(
                'rid_cod_settings',
                'colors_reset',
                __('Default colors have been restored.', 'rid-cod'),
                'updated' // 'updated' is the class for success messages
            );

            // Return 'no' so the checkbox itself doesn't stay checked after saving
            return 'no';
        }

        // If the checkbox wasn't checked, just return its sanitized value ('no')
        return 'no';
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook_suffix) {
        // Only load on our settings page
        if ($hook_suffix !== 'woocommerce_page_rid-cod-settings') {
            return;
        }

        // Enqueue the WordPress color picker styles and scripts
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Enqueue your custom admin script
        wp_enqueue_script(
            'rid-cod-admin-script',
            RID_COD_PLUGIN_URL . 'assets/js/rid-cod-admin.js',
            array('jquery', 'wp-color-picker'), // Add wp-color-picker as a dependency
            RID_COD_VERSION,
            true
        );

        // Enqueue states manager script
        wp_enqueue_script(
            'rid-cod-states-manager',
            RID_COD_PLUGIN_URL . 'assets/js/rid-cod-states-manager.js',
            array('jquery', 'jquery-ui-sortable'),
            RID_COD_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('rid-cod-states-manager', 'rid_cod_states_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('rid_cod_nonce'),
            'current_country' => get_option('rid_cod_selected_country', 'DZ')
        ));

        // Localize script for AJAX nonce and other data
        wp_localize_script('rid-cod-admin-script', 'ridCodAdminData', array(
            'clearCacheNonce' => wp_create_nonce('rid_cod_clear_cache_nonce'),
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'clearingCacheText' => __('جاري مسح ذاكرة التخزين المؤقت...', 'rid-cod'),
            'errorText' => __('خطأ:', 'rid-cod')
        ));
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook_suffix The current admin page hook.
     */
    public function enqueue_admin_scripts_old($hook_suffix) {
        // Only load on our settings page
        if ($hook_suffix !== 'woocommerce_page_rid-cod-settings') {
            return;
        }

        // Enqueue the WordPress color picker styles and scripts
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Enqueue your custom admin script
        wp_enqueue_script(
            'rid-cod-admin-script',
            RID_COD_PLUGIN_URL . 'assets/js/rid-cod-admin.js',
            array('jquery', 'wp-color-picker'), // Add wp-color-picker as a dependency
            RID_COD_VERSION,
            true
        );
    }

    /**
     * Handle AJAX request to clear shipping cache.
     */
     public function handle_clear_shipping_cache_ajax() {
         // Verify nonce
         if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'rid_cod_clear_cache_nonce')) {
             wp_send_json_error(__('Security check failed.', 'rid-cod'), 403);
         }

         // Check user permissions
         if (!current_user_can('manage_options')) {
             wp_send_json_error(__('You do not have permission to perform this action.', 'rid-cod'), 403);
         }

         // Clear the transients
         $deleted1 = delete_transient('rid_cod_wc_shipping_methods_cache');
         $deleted2 = delete_transient('rid_cod_default_costs_cache');

         // Also clear any WordPress object cache for settings
         wp_cache_flush();

         // Check if transients were deleted (or didn't exist)
         // delete_transient returns true if successful or if the transient did not exist.
         if ($deleted1 !== false && $deleted2 !== false) {
             wp_send_json_success(__('تم مسح ذاكرة التخزين المؤقت للشحن بنجاح.', 'rid-cod'));
         } else {
             wp_send_json_error(__('فشل مسح ذاكرة التخزين المؤقت للشحن.', 'rid-cod'));
         }
     }

     /**
      * Clear cache when RID COD settings are updated
      */
     public function clear_cache_on_settings_update($option_name, $old_value, $new_value) {
         // List of RID COD settings that should trigger cache clear
         $rid_cod_settings = array(
             'rid_cod_selected_country',
             'rid_cod_form_title',
             'rid_cod_field_name',
             'rid_cod_field_phone',
             'rid_cod_field_state',
             'rid_cod_field_city',
             'rid_cod_button_text',
             'rid_cod_select_city_text',
             'rid_cod_select_state_text',
             'rid_cod_select_variation_text',
             'rid_cod_free_shipping_text',
             'rid_cod_processing_text',
             'rid_cod_error_text',
             'rid_cod_shipping_unavailable_text'
         );

         // If this is a RID COD setting, clear cache
         if (in_array($option_name, $rid_cod_settings)) {
             // Clear WordPress object cache
             wp_cache_flush();

             // Clear any transients related to settings
             delete_transient('rid_cod_settings_cache');
         }
     }

     /**
      * Render country section description
      */
     public function render_country_section_description() {
         echo '<p>' . esc_html__('اختر الدولة الخاصة بمتجرك. هذا سيحدد الولايات/المدن المتاحة والتحقق من أرقام الهاتف.', 'rid-cod') . '</p>';
     }

     /**
      * Render form control section description
      */
     public function render_form_control_section_description() {
         echo '<p>' . esc_html__('تحكم في عناصر النموذج التي تريد إظهارها أو إخفاءها للعملاء.', 'rid-cod') . '</p>';
     }

     /**
      * Render states manager interface
      */
     public function render_states_manager() {
         $current_country = get_option('rid_cod_selected_country', 'DZ');
         $countries = RID_COD_Country_Manager::get_supported_countries();
         $country_name = isset($countries[$current_country]) ? $countries[$current_country]['name'] : $current_country;

         ?>
         <div id="rid-cod-states-manager">
             <div class="states-manager-header">
                 <div class="current-country-info">
                     <strong><?php esc_html_e('إدارة ولايات:', 'rid-cod'); ?></strong>
                     <span class="country-name"><?php echo esc_html($country_name); ?></span>
                     <small>(<?php esc_html_e('يمكن تغيير الدولة من تبويبة "إعدادات الدولة"', 'rid-cod'); ?>)</small>
                 </div>
                 <div class="states-actions">
                     <button type="button" id="add-new-state" class="button button-primary">
                         <?php esc_html_e('إضافة ولاية جديدة', 'rid-cod'); ?>
                     </button>
                     <button type="button" id="reset-to-default" class="button button-secondary">
                         <?php esc_html_e('استعادة الولايات الافتراضية', 'rid-cod'); ?>
                     </button>
                 </div>
             </div>

             <div id="states-list-container">
                 <div id="states-loading" style="display: none;">
                     <p><?php esc_html_e('جاري التحميل...', 'rid-cod'); ?></p>
                 </div>
                 <div id="states-list">
                     <!-- States will be loaded here via AJAX -->
                 </div>
             </div>

             <div class="states-manager-actions">
                 <button type="button" id="save-states-order" class="button button-primary" style="display: none;">
                     <?php esc_html_e('حفظ الترتيب', 'rid-cod'); ?>
                 </button>
             </div>
         </div>

         <style>
         /* States management in shipping table */
         .state-row {
             cursor: move;
         }

         .state-row:hover {
             background-color: #f9f9f9;
         }

         .drag-handle {
             cursor: move !important;
             user-select: none;
         }

         .state-actions {
             white-space: nowrap;
         }

         .state-actions .button {
             margin-right: 5px;
             font-size: 11px;
             padding: 2px 8px;
             height: auto;
             line-height: 1.4;
         }

         .states-management-actions {
             border-left: 4px solid #0073aa;
         }

         .states-management-actions h4 {
             color: #0073aa;
             margin-bottom: 10px;
         }

         /* Sortable placeholder */
         .ui-sortable-placeholder {
             background-color: #ffffcc;
             border: 2px dashed #ccc;
             height: 40px;
         }

         .ui-sortable-helper {
             background-color: #fff;
             box-shadow: 0 2px 8px rgba(0,0,0,0.2);
         }

         .states-manager-header {
             margin-bottom: 20px;
             padding: 15px;
             background: #f9f9f9;
             border: 1px solid #ddd;
             border-radius: 4px;
             display: flex;
             justify-content: space-between;
             align-items: center;
             flex-wrap: wrap;
         }

         .current-country-info {
             display: flex;
             align-items: center;
             gap: 10px;
             flex-wrap: wrap;
         }

         .current-country-info .country-name {
             background: #0073aa;
             color: white;
             padding: 4px 12px;
             border-radius: 3px;
             font-weight: bold;
         }

         .current-country-info small {
             color: #666;
             font-style: italic;
         }

         .states-actions {
             display: flex;
             gap: 10px;
             flex-wrap: wrap;
         }

         #states-list {
             border: 1px solid #ddd;
             border-radius: 4px;
             background: #fff;
             min-height: 200px;
         }

         .state-item {
             display: flex;
             align-items: center;
             padding: 10px 15px;
             border-bottom: 1px solid #eee;
             background: #fff;
             cursor: move;
         }

         .state-item:last-child {
             border-bottom: none;
         }

         .state-item:hover {
             background: #f9f9f9;
         }

         .state-item.ui-sortable-helper {
             background: #e3f2fd;
             box-shadow: 0 2px 8px rgba(0,0,0,0.2);
         }

         .state-drag-handle {
             margin-right: 10px;
             color: #666;
             cursor: move;
         }

         .state-code {
             width: 80px;
             margin-right: 15px;
             font-family: monospace;
             font-weight: bold;
         }

         .state-name {
             flex: 1;
             margin-right: 15px;
         }

         .state-actions {
             display: flex;
             gap: 5px;
         }

         .state-actions button {
             padding: 2px 8px;
             font-size: 12px;
         }

         .state-item input {
             border: 1px solid #ddd;
             padding: 4px 8px;
             border-radius: 3px;
         }

         .state-item.editing {
             background: #fff3cd;
         }

         .empty-states {
             text-align: center;
             padding: 40px;
             color: #666;
         }
         </style>
         <?php
     }

     /**
      * Render country select field
      */
     public function render_country_select_field($args) {
         $option_name = $args['id'];
         $current_value = get_option($option_name, $args['default']);
         
         // Check if Country Manager is available
         if (!class_exists('RID_COD_Country_Manager')) {
             echo '<p style="color: red;">' . esc_html__('مدير الدول غير موجود. يرجى التأكد من تفعيل الإضافة بشكل صحيح.', 'rid-cod') . '</p>';
             return;
         }
         
         $countries = RID_COD_Country_Manager::get_supported_countries();
         
         echo '<select name="' . esc_attr($option_name) . '" id="' . esc_attr($option_name) . '" onchange="ridCodCountryChanged(this)">';
         foreach ($countries as $code => $country_data) {
             $selected = selected($current_value, $code, false);
             echo '<option value="' . esc_attr($code) . '"' . $selected . '>' . esc_html($country_data['name'] . ' (' . $country_data['name_en'] . ')') . '</option>';
         }
         echo '</select>';
         
         if (isset($args['desc'])) {
             echo '<p class="description">' . esc_html($args['desc']) . '</p>';
         }
         
         // Add note about current country currency
         $current_country_data = RID_COD_Country_Manager::get_country_data($current_value);
         if ($current_country_data) {
             echo '<p class="description">';
             printf(
                 esc_html__('العملة الحالية: %s (%s)', 'rid-cod'),
                 esc_html($current_country_data['currency_symbol']),
                 esc_html($current_country_data['currency'])
             );
             echo '</p>';
         }
         
         // Add JavaScript for auto-reload on country change
         ?>
         <script type="text/javascript">
         function ridCodCountryChanged(select) {
             // Show a confirmation message
             if (confirm('<?php echo esc_js(__('تغيير الدولة سيعيد تحميل الصفحة لتحديث إعدادات الشحن. المتابعة؟', 'rid-cod')); ?>')) {
                 // Save the form and reload
                 var form = select.closest('form');
                 if (form) {
                     // Add a hidden field to indicate we're changing country
                     var hiddenField = document.createElement('input');
                     hiddenField.type = 'hidden';
                     hiddenField.name = 'rid_cod_country_changed';
                     hiddenField.value = '1';
                     form.appendChild(hiddenField);
                     
                     // Redirect to shipping tab after save
                     var actionUrl = form.action;
                     if (actionUrl.indexOf('?') === -1) {
                         form.action = actionUrl + '?page=rid-cod-settings&tab=shipping';
                     } else {
                         form.action = actionUrl + '&tab=shipping';
                     }
                     
                     form.submit();
                 }
             } else {
                 // Reset to previous value if user cancels
                 select.value = '<?php echo esc_js($current_value); ?>';
             }
         }
         </script>
         <?php
     }

     /**
      * Sanitize country code
      */
     public function sanitize_country_code($input) {
         // Check if Country Manager is available
         if (!class_exists('RID_COD_Country_Manager')) {
             return 'DZ'; // Default to Algeria if Country Manager not available
         }
         
         $countries = RID_COD_Country_Manager::get_supported_countries();
         if (isset($countries[$input])) {
             return $input;
         }
         return 'DZ'; // Default to Algeria
     }

    /**
     * Sanitize shipping costs array
     */
    public function sanitize_shipping_costs($input) {
        if (!is_array($input)) {
            return array();
        }
        
        $sanitized = array();
        foreach ($input as $state_code => $costs) {
            if (is_array($costs)) {
                $sanitized[sanitize_text_field($state_code)] = array(
                    'home' => isset($costs['home']) ? wc_format_decimal($costs['home']) : 0,
                    'desk' => isset($costs['desk']) ? wc_format_decimal($costs['desk']) : 0
                );
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize default shipping costs
     */
    public function sanitize_default_shipping($input) {
        if (!is_array($input)) {
            return array('home' => 0, 'desk' => 0);
        }

        return array(
            'home' => isset($input['home']) ? wc_format_decimal($input['home']) : 0,
            'desk' => isset($input['desk']) ? wc_format_decimal($input['desk']) : 0
        );
    }

    /**
     * Sanitize custom states
     */
    public function sanitize_custom_states($input) {
        if (!is_array($input)) {
            return array();
        }

        $sanitized = array();
        foreach ($input as $code => $name) {
            $clean_code = sanitize_text_field($code);
            $clean_name = sanitize_text_field($name);
            if (!empty($clean_code) && !empty($clean_name)) {
                $sanitized[$clean_code] = $clean_name;
            }
        }

        return $sanitized;
    }

    /**
     * Render shipping costs manager for multiple countries
     */
    public function render_shipping_costs_manager($args) {
        // Check if Country Manager and Shipping Manager are available
        if (!class_exists('RID_COD_Country_Manager') || !class_exists('RID_COD_Shipping_Manager')) {
            echo '<p style="color: red;">' . esc_html__('Required classes not found. Please make sure the plugin is properly activated.', 'rid-cod') . '</p>';
            return;
        }
        
        $current_country = RID_COD_Country_Manager::get_current_country();
        $countries = RID_COD_Country_Manager::get_supported_countries();
        $states = RID_COD_Country_Manager::get_states_by_country($current_country);
        $shipping_costs = RID_COD_Shipping_Manager::get_all_shipping_costs($current_country);
        $default_costs = RID_COD_Shipping_Manager::get_default_shipping_costs($current_country);
        
        ?>
        <div id="rid-cod-shipping-manager" style="max-width: 800px;">
            <h4><?php esc_html_e('Shipping Costs Configuration', 'rid-cod'); ?></h4>
            
            <!-- Country Selection -->
            <div class="shipping-country-selector" style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9;">
                <h5><?php esc_html_e('Current Country:', 'rid-cod'); ?> 
                    <span style="color: #0073aa;">
                        <?php 
                        $country_data = RID_COD_Country_Manager::get_country_data($current_country);
                        echo esc_html($country_data ? $country_data['name'] : $current_country); 
                        ?>
                    </span>
                </h5>
                <p><?php esc_html_e('Shipping costs are configured per country. Change the country in the "Country Settings" section above to configure costs for a different country.', 'rid-cod'); ?></p>
                
                <!-- Default Costs -->
                <div style="margin-top: 15px;">
                    <h6><?php esc_html_e('Default Shipping Costs (when state-specific cost is not set):', 'rid-cod'); ?></h6>
                    <table style="margin-top: 10px;">
                        <tr>
                            <td style="padding-right: 15px;">
                                <label><?php esc_html_e('Home Delivery:', 'rid-cod'); ?></label>
                                <input type="number" 
                                       id="default_shipping_home_<?php echo esc_attr($current_country); ?>" 
                                       name="rid_cod_default_shipping_<?php echo esc_attr($current_country); ?>[home]"
                                       value="<?php echo esc_attr($default_costs['home'] ?? 0); ?>" 
                                       step="0.01" 
                                       min="0" 
                                       style="width: 100px;" />
                                <span style="margin-left: 5px;"><?php echo esc_html($country_data['currency_symbol'] ?? 'د.ج'); ?></span>
                            </td>
                            <td>
                                <label><?php esc_html_e('Desk Delivery:', 'rid-cod'); ?></label>
                                <input type="number" 
                                       id="default_shipping_desk_<?php echo esc_attr($current_country); ?>" 
                                       name="rid_cod_default_shipping_<?php echo esc_attr($current_country); ?>[desk]"
                                       value="<?php echo esc_attr($default_costs['desk'] ?? 0); ?>" 
                                       step="0.01" 
                                       min="0" 
                                       style="width: 100px;" />
                                <span style="margin-left: 5px;"><?php echo esc_html($country_data['currency_symbol'] ?? 'د.ج'); ?></span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <?php if (!empty($states)): ?>
            <!-- State-Specific Costs -->
            <div class="shipping-states-costs">
                <h5><?php esc_html_e('State-Specific Shipping Costs:', 'rid-cod'); ?></h5>
                <p><?php esc_html_e('Set specific costs for each state. Leave empty to use default costs.', 'rid-cod'); ?></p>
                
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: white;">
                    <table class="wp-list-table widefat fixed striped" style="margin-top: 0;">
                        <thead>
                            <tr>
                                <th style="width: 40%;"><?php esc_html_e('State', 'rid-cod'); ?></th>
                                <th style="width: 30%;"><?php esc_html_e('Home Delivery', 'rid-cod'); ?></th>
                                <th style="width: 30%;"><?php esc_html_e('Desk Delivery', 'rid-cod'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($states as $state_code => $state_name): 
                                $home_cost = $shipping_costs[$state_code]['home'] ?? '';
                                $desk_cost = $shipping_costs[$state_code]['desk'] ?? '';
                            ?>
                            <tr>
                                <td><strong><?php echo esc_html($state_name); ?></strong></td>
                                <td>
                                    <input type="number" 
                                           name="rid_cod_shipping_costs_<?php echo esc_attr($current_country); ?>[<?php echo esc_attr($state_code); ?>][home]"
                                           value="<?php echo esc_attr($home_cost); ?>" 
                                           step="0.01" 
                                           min="0" 
                                           style="width: 80px;" 
                                           placeholder="<?php echo esc_attr($default_costs['home'] ?? 0); ?>" />
                                    <small style="color: #666;"><?php echo esc_html($country_data['currency_symbol'] ?? 'د.ج'); ?></small>
                                </td>
                                <td>
                                    <input type="number" 
                                           name="rid_cod_shipping_costs_<?php echo esc_attr($current_country); ?>[<?php echo esc_attr($state_code); ?>][desk]"
                                           value="<?php echo esc_attr($desk_cost); ?>" 
                                           step="0.01" 
                                           min="0" 
                                           style="width: 80px;" 
                                           placeholder="<?php echo esc_attr($default_costs['desk'] ?? 0); ?>" />
                                    <small style="color: #666;"><?php echo esc_html($country_data['currency_symbol'] ?? 'د.ج'); ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div style="margin-top: 15px; padding: 10px; background: #e7f3ff; border-left: 4px solid #0073aa;">
                    <p><strong><?php esc_html_e('Tips:', 'rid-cod'); ?></strong></p>
                    <ul style="margin-left: 20px;">
                        <li><?php esc_html_e('Empty fields will use the default costs shown above.', 'rid-cod'); ?></li>
                        <li><?php esc_html_e('Set cost to 0 for free shipping to specific states.', 'rid-cod'); ?></li>
                        <li><?php esc_html_e('Changes are saved automatically when you click "Save Changes" button.', 'rid-cod'); ?></li>
                    </ul>
                </div>
            </div>
            <?php else: ?>
            <div style="padding: 20px; border: 1px solid #ddd; background: #fff3cd; color: #856404;">
                <p><?php esc_html_e('No states found for the selected country. Please make sure the country data is properly configured.', 'rid-cod'); ?></p>
            </div>
            <?php endif; ?>
        </div>
        
        <style>
        #rid-cod-shipping-manager input[type="number"] {
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        #rid-cod-shipping-manager input[type="number"]:focus {
            border-color: #0073aa;
            box-shadow: 0 0 0 1px #0073aa;
        }
        #rid-cod-shipping-manager table th {
            background: #f1f1f1;
            font-weight: 600;
            padding: 8px;
        }
        #rid-cod-shipping-manager table td {
            padding: 8px;
            vertical-align: middle;
        }
        </style>
        <?php
    }

}