/* Modern Form Style for RID COD Plugin - Exact Match to Reference Image */

/* Modern Form Container - Matching the purple rounded design */
.rid-cod-form-modern #rid-cod-checkout {
    background: #ffffff;
    padding: 25px;
    border-radius: 20px;
    border: 3px solid #8b5cf6;
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.15);
    margin-bottom: 30px;
    max-width: 600px;
    margin: 0 auto 30px;
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    direction: rtl;
    position: relative;
    overflow: visible;
}

/* Remove the gradient animation - not in reference image */

/* Title matching reference image */
.rid-cod-form-modern .rid-cod-title h3 {
    font-size: 18px;
    margin-bottom: 25px;
    text-align: center;
    color: #374151;
    font-weight: 600;
    position: relative;
    padding-bottom: 0;
    line-height: 1.5;
}

/* Remove the underline - not in reference */
.rid-cod-form-modern .rid-cod-title h3::after {
    display: none;
}

/* Modern Form Layout */
.rid-cod-form-modern #rid-cod-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rid-cod-form-modern .rid-cod-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

/* Input Fields matching reference image */
.rid-cod-form-modern #rid-cod-form input[type="text"],
.rid-cod-form-modern #rid-cod-form input[type="tel"],
.rid-cod-form-modern #rid-cod-form input[type="email"],
.rid-cod-form-modern #rid-cod-form select,
.rid-cod-form-modern #rid-cod-form textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 12px;
    font-size: 14px;
    color: #6b7280;
    background: #f9fafb;
    transition: all 0.2s ease;
    box-shadow: none;
    height: auto;
    min-height: 48px;
    font-weight: 400;
}

.rid-cod-form-modern #rid-cod-form input:focus,
.rid-cod-form-modern #rid-cod-form select:focus,
.rid-cod-form-modern #rid-cod-form textarea:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    transform: none;
    background: #ffffff;
}

.rid-cod-form-modern #rid-cod-form input::placeholder,
.rid-cod-form-modern #rid-cod-form select::placeholder,
.rid-cod-form-modern #rid-cod-form textarea::placeholder {
    color: #a0aec0;
    font-weight: 500;
}

/* Modern Field Groups */
.rid-cod-form-modern .rid-cod-field-group {
    position: relative;
    margin-bottom: 0;
}

.rid-cod-form-modern .rid-cod-field-with-icon {
    position: relative;
}

.rid-cod-form-modern .rid-input-icon {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: #8b5cf6;
    font-size: 20px;
    z-index: 2;
    pointer-events: none;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.rid-cod-form-modern .rid-cod-field-with-icon:focus-within .rid-input-icon {
    color: #8b5cf6;
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

.rid-cod-form-modern .rid-cod-field-with-icon input,
.rid-cod-form-modern .rid-cod-field-with-icon select {
    padding-right: 55px;
}

/* Variations section matching reference image */
.rid-cod-form-modern .rid-cod-variations {
    background: transparent;
    border: none;
    border-radius: 0;
    padding: 0;
    margin-bottom: 20px;
    box-shadow: none;
    transition: none;
    position: relative;
}

/* Remove the top border */
.rid-cod-form-modern .rid-cod-variations::before {
    display: none;
}

/* Remove hover effects */
.rid-cod-form-modern .rid-cod-variations:hover {
    border-color: transparent;
    box-shadow: none;
    transform: none;
}

/* Variation layout matching reference */
.rid-cod-form-modern .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
    align-items: center;
}

.rid-cod-form-modern .variation-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.rid-cod-form-modern .variation-label .attribute-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0;
    margin-left: 10px;
}

/* Size options matching reference */
.rid-cod-form-modern .variation-option {
    padding: 8px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 600;
    color: #374151;
    min-width: 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
    font-size: 14px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Remove animation effects */
.rid-cod-form-modern .variation-option::before {
    display: none;
}

.rid-cod-form-modern .variation-option:hover {
    border-color: #8b5cf6;
    transform: none;
    box-shadow: none;
}

/* Selected state matching reference - black border for selected size */
.rid-cod-form-modern .variation-option.selected {
    border-color: #000000;
    background: #ffffff;
    color: #000000;
    transform: none;
    box-shadow: none;
    border-width: 3px;
}

/* Color swatches matching reference image */
.rid-cod-form-modern .variation-option.color-option {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #d1d5db;
    min-width: 32px;
}

.rid-cod-form-modern .color-swatch {
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: none;
    box-shadow: none;
    transition: none;
}

.rid-cod-form-modern .variation-option.color-option.selected {
    border-color: #000000;
    transform: none;
    box-shadow: none;
    border-width: 3px;
}

.rid-cod-form-modern .variation-option.color-option.selected .color-swatch {
    transform: none;
    box-shadow: none;
}

/* Actions row matching reference */
.rid-cod-form-modern .rid-cod-actions-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 20px;
    padding: 0;
    background: transparent;
    border-radius: 0;
    border: none;
}

/* Submit button matching reference */
.rid-cod-form-modern #rid-cod-submit-btn {
    background: #8b5cf6;
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: none;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    flex: 1;
}

.rid-cod-form-modern #rid-cod-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.rid-cod-form-modern #rid-cod-submit-btn:hover::before {
    left: 100%;
}

.rid-cod-form-modern #rid-cod-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(106, 61, 232, 0.4);
}

.rid-cod-form-modern #rid-cod-submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(106, 61, 232, 0.3);
}

/* Modern Quantity Selector */
.rid-cod-form-modern .rid-cod-quantity-selector {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 2px solid #e8ecf4;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.rid-cod-form-modern .rid-cod-quantity-selector:hover {
    border-color: #8b5cf6;
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.15);
}

.rid-cod-form-modern .rid-cod-quantity-selector button {
    background: #f8fafc;
    border: none;
    width: 44px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 800;
    color: #8b5cf6;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.rid-cod-form-modern .rid-cod-quantity-selector button:hover {
    background: #8b5cf6;
    color: #ffffff;
    transform: scale(1.05);
}

.rid-cod-form-modern .rid-cod-quantity-selector button:active {
    transform: scale(0.95);
}

.rid-cod-form-modern .rid-cod-quantity-selector input {
    border: none;
    width: 60px;
    height: 48px;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    background: #ffffff;
    margin: 0;
    padding: 0;
    min-height: auto;
}

.rid-cod-form-modern .rid-cod-quantity-selector input:focus {
    outline: none;
    box-shadow: none;
    transform: none;
}

/* Modern Order Summary */
.rid-cod-form-modern #rid-cod-summary-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e8ecf4;
    border-radius: 18px;
    margin-top: 30px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.rid-cod-form-modern #rid-cod-summary-header {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 20px;
    border-bottom: 2px solid #e8ecf4;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rid-cod-form-modern #rid-cod-summary-header:hover {
    background: linear-gradient(135deg, #edf2f7, #e2e8f0);
}

.rid-cod-form-modern #rid-cod-summary-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.rid-cod-form-modern #rid-cod-summary-content {
    padding: 20px;
}

.rid-cod-form-modern #rid-cod-summary-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.rid-cod-form-modern #rid-cod-summary-content td {
    padding: 12px 0;
    border-bottom: 1px solid #e8ecf4;
    font-size: 15px;
}

.rid-cod-form-modern #rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    font-weight: 700;
    font-size: 16px;
    color: #6a3de8;
    padding-top: 16px;
}

/* Modern WhatsApp Button */
.rid-cod-form-modern .rid-cod-whatsapp-button {
    margin-top: 20px;
}

.rid-cod-form-modern #rid-cod-whatsapp-btn {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: #ffffff;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(37, 211, 102, 0.3);
    width: 100%;
    justify-content: center;
}

.rid-cod-form-modern #rid-cod-whatsapp-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(37, 211, 102, 0.4);
    text-decoration: none;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rid-cod-form-modern #rid-cod-checkout {
        padding: 20px;
        border-radius: 12px;
        margin: 0 10px 20px;
    }
    
    .rid-cod-form-modern .rid-cod-customer-info {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .rid-cod-form-modern .rid-cod-actions-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .rid-cod-form-modern .rid-cod-submit {
        order: 2;
    }
    
    .rid-cod-form-modern .rid-cod-quantity {
        order: 1;
        justify-content: center;
    }
    
    .rid-cod-form-modern .variation-boxes {
        gap: 8px;
    }
    
    .rid-cod-form-modern .variation-option {
        padding: 10px 16px;
        min-width: 50px;
    }
    
    .rid-cod-form-modern .variation-option.color-option {
        width: 40px;
        height: 40px;
    }
}
