/* Modern Form Style for RID COD Plugin */

/* Modern Form Container */
.rid-cod-form-modern #rid-cod-checkout {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    padding: 30px;
    border-radius: 20px;
    border: 1px solid #e8ecf4;
    box-shadow: 0 12px 40px rgba(106, 61, 232, 0.12);
    margin-bottom: 30px;
    max-width: 650px;
    margin: 0 auto 30px;
    font-family: 'Cairo', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    direction: rtl;
    position: relative;
    overflow: hidden;
}

.rid-cod-form-modern #rid-cod-checkout::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6a3de8, #9c5dff, #6a3de8);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Modern Title */
.rid-cod-form-modern .rid-cod-title h3 {
    font-size: 22px;
    margin-bottom: 35px;
    text-align: center;
    color: #1a202c;
    font-weight: 800;
    position: relative;
    padding-bottom: 20px;
    line-height: 1.4;
}

.rid-cod-form-modern .rid-cod-title h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #6a3de8, #9c5dff);
    border-radius: 2px;
}

/* Modern Form Layout */
.rid-cod-form-modern #rid-cod-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rid-cod-form-modern .rid-cod-customer-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 0;
}

/* Modern Input Fields */
.rid-cod-form-modern #rid-cod-form input[type="text"],
.rid-cod-form-modern #rid-cod-form input[type="tel"],
.rid-cod-form-modern #rid-cod-form input[type="email"],
.rid-cod-form-modern #rid-cod-form select,
.rid-cod-form-modern #rid-cod-form textarea {
    width: 100%;
    padding: 18px 22px;
    border: 2px solid #e2e8f0;
    border-radius: 14px;
    font-size: 15px;
    color: #2d3748;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.04);
    height: auto;
    min-height: 56px;
    font-weight: 500;
}

.rid-cod-form-modern #rid-cod-form input:focus,
.rid-cod-form-modern #rid-cod-form select:focus,
.rid-cod-form-modern #rid-cod-form textarea:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.12), 0 6px 16px rgba(139, 92, 246, 0.18);
    transform: translateY(-2px);
    background: #ffffff;
}

.rid-cod-form-modern #rid-cod-form input::placeholder,
.rid-cod-form-modern #rid-cod-form select::placeholder,
.rid-cod-form-modern #rid-cod-form textarea::placeholder {
    color: #a0aec0;
    font-weight: 500;
}

/* Modern Field Groups */
.rid-cod-form-modern .rid-cod-field-group {
    position: relative;
    margin-bottom: 0;
}

.rid-cod-form-modern .rid-cod-field-with-icon {
    position: relative;
}

.rid-cod-form-modern .rid-input-icon {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: #8b5cf6;
    font-size: 20px;
    z-index: 2;
    pointer-events: none;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.rid-cod-form-modern .rid-cod-field-with-icon:focus-within .rid-input-icon {
    color: #8b5cf6;
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

.rid-cod-form-modern .rid-cod-field-with-icon input,
.rid-cod-form-modern .rid-cod-field-with-icon select {
    padding-right: 55px;
}

/* Modern Variations */
.rid-cod-form-modern .rid-cod-variations {
    background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
    border: 2px solid #e8ecf4;
    border-radius: 18px;
    padding: 28px;
    margin-bottom: 25px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
}

.rid-cod-form-modern .rid-cod-variations::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #8b5cf6, #a855f7, #8b5cf6);
    border-radius: 18px 18px 0 0;
}

.rid-cod-form-modern .rid-cod-variations:hover {
    border-color: #8b5cf6;
    box-shadow: 0 10px 28px rgba(139, 92, 246, 0.12);
    transform: translateY(-2px);
}

.rid-cod-form-modern .variation-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
    margin-top: 18px;
    justify-content: center;
}

.rid-cod-form-modern .variation-label .attribute-label {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 12px;
    text-align: center;
}

.rid-cod-form-modern .variation-option {
    padding: 14px 24px;
    border: 2px solid #e8ecf4;
    border-radius: 14px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 700;
    color: #4a5568;
    min-width: 65px;
    text-align: center;
    position: relative;
    overflow: hidden;
    font-size: 15px;
}

.rid-cod-form-modern .variation-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(106, 61, 232, 0.1), transparent);
    transition: left 0.5s ease;
}

.rid-cod-form-modern .variation-option:hover::before {
    left: 100%;
}

.rid-cod-form-modern .variation-option:hover {
    border-color: #6a3de8;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(106, 61, 232, 0.15);
}

.rid-cod-form-modern .variation-option.selected {
    border-color: #8b5cf6;
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.35);
}

/* Modern Color Swatches */
.rid-cod-form-modern .variation-option.color-option {
    width: 45px;
    height: 45px;
    padding: 3px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid transparent;
}

.rid-cod-form-modern .color-swatch {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.rid-cod-form-modern .variation-option.color-option.selected {
    border-color: #8b5cf6;
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.3);
}

.rid-cod-form-modern .variation-option.color-option.selected .color-swatch {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
}

/* Modern Actions Row */
.rid-cod-form-modern .rid-cod-actions-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 24px;
    align-items: center;
    margin-top: 35px;
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 16px;
    border: 1px solid #e8ecf4;
}

/* Modern Submit Button */
.rid-cod-form-modern #rid-cod-submit-btn {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
    color: #ffffff;
    border: none;
    padding: 18px 40px;
    border-radius: 16px;
    font-size: 17px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.35);
    position: relative;
    overflow: hidden;
    min-height: 56px;
    width: 100%;
}

.rid-cod-form-modern #rid-cod-submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.rid-cod-form-modern #rid-cod-submit-btn:hover::before {
    left: 100%;
}

.rid-cod-form-modern #rid-cod-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(106, 61, 232, 0.4);
}

.rid-cod-form-modern #rid-cod-submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(106, 61, 232, 0.3);
}

/* Modern Quantity Selector */
.rid-cod-form-modern .rid-cod-quantity-selector {
    display: flex;
    align-items: center;
    background: #ffffff;
    border: 2px solid #e8ecf4;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.rid-cod-form-modern .rid-cod-quantity-selector:hover {
    border-color: #8b5cf6;
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.15);
}

.rid-cod-form-modern .rid-cod-quantity-selector button {
    background: #f8fafc;
    border: none;
    width: 44px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 800;
    color: #8b5cf6;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.rid-cod-form-modern .rid-cod-quantity-selector button:hover {
    background: #8b5cf6;
    color: #ffffff;
    transform: scale(1.05);
}

.rid-cod-form-modern .rid-cod-quantity-selector button:active {
    transform: scale(0.95);
}

.rid-cod-form-modern .rid-cod-quantity-selector input {
    border: none;
    width: 60px;
    height: 48px;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    background: #ffffff;
    margin: 0;
    padding: 0;
    min-height: auto;
}

.rid-cod-form-modern .rid-cod-quantity-selector input:focus {
    outline: none;
    box-shadow: none;
    transform: none;
}

/* Modern Order Summary */
.rid-cod-form-modern #rid-cod-summary-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e8ecf4;
    border-radius: 18px;
    margin-top: 30px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.rid-cod-form-modern #rid-cod-summary-header {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 20px;
    border-bottom: 2px solid #e8ecf4;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rid-cod-form-modern #rid-cod-summary-header:hover {
    background: linear-gradient(135deg, #edf2f7, #e2e8f0);
}

.rid-cod-form-modern #rid-cod-summary-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.rid-cod-form-modern #rid-cod-summary-content {
    padding: 20px;
}

.rid-cod-form-modern #rid-cod-summary-content table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.rid-cod-form-modern #rid-cod-summary-content td {
    padding: 12px 0;
    border-bottom: 1px solid #e8ecf4;
    font-size: 15px;
}

.rid-cod-form-modern #rid-cod-summary-content tr:last-child td {
    border-bottom: none;
    font-weight: 700;
    font-size: 16px;
    color: #6a3de8;
    padding-top: 16px;
}

/* Modern WhatsApp Button */
.rid-cod-form-modern .rid-cod-whatsapp-button {
    margin-top: 20px;
}

.rid-cod-form-modern #rid-cod-whatsapp-btn {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: #ffffff;
    border: none;
    padding: 16px 32px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(37, 211, 102, 0.3);
    width: 100%;
    justify-content: center;
}

.rid-cod-form-modern #rid-cod-whatsapp-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(37, 211, 102, 0.4);
    text-decoration: none;
    color: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rid-cod-form-modern #rid-cod-checkout {
        padding: 20px;
        border-radius: 12px;
        margin: 0 10px 20px;
    }
    
    .rid-cod-form-modern .rid-cod-customer-info {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .rid-cod-form-modern .rid-cod-actions-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .rid-cod-form-modern .rid-cod-submit {
        order: 2;
    }
    
    .rid-cod-form-modern .rid-cod-quantity {
        order: 1;
        justify-content: center;
    }
    
    .rid-cod-form-modern .variation-boxes {
        gap: 8px;
    }
    
    .rid-cod-form-modern .variation-option {
        padding: 10px 16px;
        min-width: 50px;
    }
    
    .rid-cod-form-modern .variation-option.color-option {
        width: 40px;
        height: 40px;
    }
}
